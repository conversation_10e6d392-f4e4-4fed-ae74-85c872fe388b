#import '../ClassStaffPreferencesFragment.graphql'

fragment PreferencesFragment on PersonPreferences {
  id
  EDIT_SESSION
  ORGANISATION_GROUP_ID_FILTER
  UI_LANGUAGE
  TIME_ZONE
  MAIN_ORGANISATION
  TIMETABLE_STAFF_COLUMNS
  TIMETABLE_STUDENT_COLUMNS
  NOTIFICATION_ED_COM_ORGANISATION {
    orgStructureLevelId
    organisationId
    personEntityAllocationId
    emailAddress
  }
  ED_COM_MESSAGES_ORGANISATION {
    organisationId
    orgStructureLevelId
    personEntityAllocationId
  }
  QUICK_LINK_SETTINGS
  CLASS_STAFF_PREFERENCES {
    ...ClassStaffPreferencesMain
  }
  WEEK_STARTS_FROM
  LAST_PATHNAME_MODULES {
    module
    route
  }
  E_CONTENT_SELECTED_RESOURCE {
    id
    folderId
    name
    sequence
  }
  E_CONTENT_QUESTIONS_COUNT
  E_CONTENT_GPT_VERSION
  E_CONTENT_TEXT_TRANSFORM_GPT_VERSION
  LMS_PROGRAM_INTAKE_FILTERS {
    INTAKE_STATUS
    ORGANISATIONS {
      id
      name
      status
      orgGroupId
      organisationId
      parentId
      organisationType
      organisationStructureId
      sequence
      type
    }
    PROGRAM_GROUP
    PROGRAMS {
      id
      name
      programGroupTypeId
      programGroupId
      programId
      organisationStructureLevelId
      status
      intakeId
      treeSequence {
        parentSequence
        sequence
      }
    }
  }
  E_CONTENT_RESOURCE_LIBRARY_FILTER {
    LIBRARY_ID
    STATUS
  }
  LMS_STAFF_LEARNING_SPACE_FILTERS {
    INTAKE_STATUS
    ORGANISATION_GROUP {
      id
      name
      status
    }
    ORGANISATIONS {
      id
      name
      status
      orgGroupId
      organisationId
      parentId
      organisationType
      organisationStructureId
      sequence
      type
    }
    PROGRAM_GROUP
    PROGRAMS {
      id
      name
      programGroupTypeId
      programGroupId
      programId
      organisationStructureLevelId
      status
      intakeId
      treeSequence {
        parentSequence
        sequence
      }
    }
  }
  LMS_STUDENT_LEARNING_SPACE_FILTERS {
    INTAKE_STATUS
    ORGANISATION_GROUP {
      id
      name
      status
    }
    ORGANISATIONS {
      id
      name
      status
      orgGroupId
      organisationId
      parentId
      organisationType
      organisationStructureId
      sequence
      type
    }
    PROGRAM_GROUP
    PROGRAMS {
      id
      name
      programGroupTypeId
      programGroupId
      programId
      organisationStructureLevelId
      status
      intakeId
      treeSequence {
        parentSequence
        sequence
      }
    }
  }
  LMS_TIMETABLE_FILTERS {
    STATUS
    ORGANISATIONS {
      id
      name
      status
      orgGroupId
      organisationId
      parentId
      organisationType
      organisationStructureId
      sequence
      type
    }
  }
  E_CONTENT_TRANSLATION_SUPPORT_SUMMARY {
    LANGUAGE_TO
  }
  E_CONTENT_TRANSLATION_SUPPORT_COMPARE {
    LANGUAGE_FROM
    LANGUAGE_TO
  }
  E_CONTENT_TRANSLATION_SUPPORT_SEARCH {
    LANGUAGE_ID
  }
  TEXT_TO_AUDIO {
    AI_MODEL
    LANGUAGE
    GENDER
    SPEAKER
    SPEED
    PITCH
    VOLUME_GAIN
    FREQUENCY
    AGE_GROUP
    STYLE
  }
}
