import { useQuery } from 'react-apollo';
import useSock<PERSON><PERSON><PERSON>ler from '../socket/useSocketHandler';
import unreadMessagesCountQuery from './data/unreadMessagesCount.graphql';

export default function useUnreadMessagesCount(
  messageId: number,
): {
  unreadMessagesCount: number | undefined;
  refetch: () => void;
} {
  const { data: { unreadMessagesCount } = {}, refetch } = useQuery<{
    unreadMessagesCount: number;
  }>(unreadMessagesCountQuery, {
    variables: { messageId, scope: messageId },
    fetchPolicy: 'no-cache',
    skip: !messageId,
  });
  console.log('messageId', messageId);


  useSocketHandler('message', refetch);

  return {
    unreadMessagesCount,
    refetch,
  };
}
