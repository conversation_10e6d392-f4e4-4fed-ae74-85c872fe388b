import PropTypes from 'prop-types';
import React from 'react';
import { withRouter } from 'react-router-dom';

import Tabs from '../../../../common/components/utils/Tabs';

import withTranslations from '../../../../common/components/utils/Translations/withTranslations';
import translatePropTypes from '../../../../common/translatePropTypes';
import EplusPropTypes, { AUDIO } from '../../../propTypes';

import UploadFileTab from './UploadFileTab';

import styles from './UploadFileTabs.scss';
import UploadPhotoTab from './UploadPhotoTab';
import UploadVideoTab from './UploadVideoTab';
import UploadAudioTab from './UploadAudioTab';
import TextToAudioTab from './TextToAudioTab';

const isMediaRecorderSupported =
  typeof window !== 'undefined' && !window.MediaRecorder.notSupported;

@withRouter
@withTranslations
export default class UploadFileTabs extends React.PureComponent {
  static propTypes = {
    onDropFile: PropTypes.func.isRequired,
    updateUploading: PropTypes.func.isRequired,
    onRemoveFile: PropTypes.func.isRequired,
    isMultiple: PropTypes.bool.isRequired,
    allowedFileTypes: PropTypes.oneOfType([
      PropTypes.oneOf(EplusPropTypes.allowedFileTypes),
      PropTypes.arrayOf(PropTypes.oneOf(EplusPropTypes.allowedFileTypes)),
    ]).isRequired,
    hasPhotoRecorder: PropTypes.bool,
    hasVideoRecorder: PropTypes.bool,
    hasAudioRecorder: PropTypes.bool,
    hasRoutes: PropTypes.bool,
    onValidate: PropTypes.func,
    files: PropTypes.arrayOf(EplusPropTypes.fileAttachment),
    maxSize: PropTypes.number,
    maxDuration: PropTypes.number,
    isUploading: PropTypes.bool,
    handleChangeCapture: PropTypes.func,
    ...translatePropTypes,
  };

  static defaultProps = {
    hasRoutes: false,
    onValidate: null,
    hasPhotoRecorder: true,
    hasVideoRecorder: false,
    hasAudioRecorder: false,
    files: [],
    isUploading: false,
  };

  render() {
    const {
      t,
      isMultiple,
      allowedFileTypes,
      onValidate,
      hasPhotoRecorder,
      hasVideoRecorder,
      hasAudioRecorder,
      hasRoutes,
      files,
      onDropFile,
      updateUploading,
      onRemoveFile,
      maxSize,
      maxDuration,
      isUploading,
      handleChangeCapture,
      match,
      isTextToAudioEnabled,
      audioValidation,
      audioValidationParameters,
    } = this.props;
    const onlyAudios = allowedFileTypes === AUDIO;
    return (
      <div className={styles['tabs-wrapper']}>
        <Tabs
          horizontal
          // className="tabs-second"
          hasRoutes={hasRoutes}
          saveTabs={false}
        >
          <Tabs.Tab default title={t('Upload')}>
            <UploadFileTab
              accept={allowedFileTypes}
              audioValidation={audioValidation}
              audioValidationParameters={audioValidationParameters}
              files={files}
              isMultiple={isMultiple}
              isUploading={isUploading}
              maxDuration={maxDuration}
              maxSize={maxSize}
              tab="Files"
              onDropFile={onDropFile}
              onRemoveFile={onRemoveFile}
              onValidate={onValidate}
            />
          </Tabs.Tab>
          {isTextToAudioEnabled && onlyAudios && (
            <Tabs.Tab route="text-to-audio" title={t('Text to Audio')}>
              <TextToAudioTab
                contentId={
                  match.params?.id || match.params?.contentId || undefined
                }
                files={files}
                handleChangeCapture={handleChangeCapture}
                isUploading={isUploading}
                maxDuration={maxDuration}
                maxSize={maxSize}
                tab="TextToAudio"
                updateUploading={updateUploading}
                onDropFile={onDropFile}
                onRemoveFile={onRemoveFile}
                onValidate={onValidate}
              />
            </Tabs.Tab>
          )}
          {hasPhotoRecorder && isMediaRecorderSupported && (
            <Tabs.Tab route="take-photo" title={t('Take Photo')}>
              <UploadPhotoTab
                files={files}
                isUploading={isUploading}
                maxSize={maxSize}
                tab="Photos"
                onDropFile={onDropFile}
                onRemoveFile={onRemoveFile}
              />
            </Tabs.Tab>
          )}
          {hasVideoRecorder && isMediaRecorderSupported && (
            <Tabs.Tab route="take-video" title={t('Record Video')}>
              <UploadVideoTab
                files={files}
                handleChangeCapture={handleChangeCapture}
                isUploading={isUploading}
                maxSize={maxSize}
                tab="Videos"
                onDropFile={onDropFile}
                onRemoveFile={onRemoveFile}
              />
            </Tabs.Tab>
          )}
          {hasAudioRecorder && (
            <Tabs.Tab route="take-audio" title={t('Record Audio')}>
              <UploadAudioTab
                files={files}
                handleChangeCapture={handleChangeCapture}
                isUploading={isUploading}
                maxDuration={maxDuration}
                maxSize={maxSize}
                tab="Audios"
                onDropFile={onDropFile}
                onRemoveFile={onRemoveFile}
                onValidate={onValidate}
              />
            </Tabs.Tab>
          )}
        </Tabs>
      </div>
    );
  }
}
