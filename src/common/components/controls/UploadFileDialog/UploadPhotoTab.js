import PropTypes from 'prop-types';
import React from 'react';
import uuidv4 from 'uuid/v4';
import { round } from 'lodash';
import classNames from 'classnames';
import imageCompression from 'browser-image-compression';

import Alert from '../../../../common/components/utils/Alert';
import Icon from '../../../../common/components/utils/Icon';

import Ripple from '../../../../common/components/utils/Ripple';

import withTranslations from '../../../../common/components/utils/Translations/withTranslations';
import WebCam from '../../../../common/components/utils/WebCam';
import EplusPropTypes from '../../../../common/propTypes';
import translatePropTypes from '../../../../common/translatePropTypes';
import { ErrorWithHumanMessage } from '../../../errors';
import { toKb } from '../../../fileSizeHelper';
import swal2, { Swal } from '../../../utils/swal2';
import styles from './UploadFileTab.scss';

const ROUND_TO_ONE = 1;
const ROUND_TO = 3;
const BYTES_IN_MEGABYTE = 1048576;
const BYTES_IN_KILOBYTE = 1024;

@withTranslations
export default class UploadPhotoTab extends React.PureComponent {
  static propTypes = {
    tab: EplusPropTypes.fileUploadTab.isRequired,
    onDropFile: PropTypes.func.isRequired,
    onRemoveFile: PropTypes.func.isRequired,
    files: PropTypes.arrayOf(EplusPropTypes.fileAttachment).isRequired,
    maxSize: PropTypes.number,
    ...translatePropTypes,
  };

  constructor(props) {
    super(props);
    const { files, tab } = props;
    const prevPicture = files.find(file => file.type === tab);
    this.state = {
      playCamera: true,
      videoMode: !prevPicture,
      imageUrl: prevPicture ? URL.createObjectURL(prevPicture.data) : null,
      videoError: null,
    };
  }

  pictureName = `${uuidv4()}.png`;

  handleWebCamToggle = () => {
    this.setState(prevState => ({
      videoMode: !prevState.videoMode,
      imageUrl: null,
    }));
  };

  handleCameraError = error => {
    this.setState({ videoError: error });
  };

  handleRetakePicture = () => {
    const { onRemoveFile, files, tab } = this.props;
    files.forEach(file => file.type === tab && onRemoveFile(file.uploadToken));
    this.handleWebCamToggle();
  };

  handleResizeAndUpload = async imageBlob => {
    const { maxSize, onDropFile, tab } = this.props;
    const { imageUrl } = this.state;

    if (!maxSize) return;
    const file = new File([imageBlob], this.pictureName, {
      type: 'image/png',
    });
    const compressFile = async inputFile => {
      const compressedFile = await imageCompression(inputFile, {
        maxSizeMB: maxSize / BYTES_IN_MEGABYTE,
        useWebWorker: true,
      });
      if (compressedFile.size > maxSize) {
        return await compressFile(compressedFile);
      }
      return compressedFile;
    };

    const finalCompressedFile = await compressFile(file);

    const newFile = new File([finalCompressedFile], this.pictureName, {
      type: 'image/png',
    });

    const blob = new Blob([newFile], { type: file.type });
    onDropFile({
      type: tab,
      fileName: this.pictureName,
      mimeType: 'image/png',
      fileSize: blob.size,
      description: '',
      data: blob,
      preview: imageUrl,
    });
  };

  checkFileSize = async blob => {
    const { maxSize, t } = this.props;
    if (maxSize && blob.size > maxSize) {
      await swal2({
        text: t(`Photo exceeds the maximum allowed size of #{maxSize}`, {
          maxSize:
            round(maxSize / BYTES_IN_MEGABYTE, ROUND_TO) >= 1
              ? `${round(maxSize / BYTES_IN_MEGABYTE, ROUND_TO_ONE)} MB`
              : `${round(maxSize / BYTES_IN_KILOBYTE)} KB`,
        }),
        icon: 'info',
        className: classNames(styles.modalWrapper),
        confirmButtonText: t('Resize'),
        cancelButtonText: t('Cancel'),
        preConfirm: async () => {
          const confirmButton = Swal.getConfirmButton();
          if (confirmButton) {
            confirmButton.innerHTML = '<i class="icon-spinner4 spinner" />';
            confirmButton.disabled = true; // Disable the button while loading
          }

          await this.handleResizeAndUpload(blob);
        },
      });
      return false;
    }
    return true;
  };

  takePicture = canvas => {
    const { onDropFile, tab } = this.props;

    const imageUrl = canvas.toDataURL();
    this.setState({
      imageUrl,
    });
    canvas.toBlob(async blob => {
      const response = await this.checkFileSize(blob);
      if (!response) return;
      onDropFile({
        type: tab,
        fileName: this.pictureName,
        mimeType: 'image/png',
        fileSize: blob.size,
        description: '',
        data: blob,
        preview: imageUrl,
      });
    });
  };

  render() {
    const { t, files, tab } = this.props;
    const { playCamera, videoMode, videoError, imageUrl } = this.state;
    const items = files.filter(f => f.type === tab);
    const uploadError = items && items.length ? items[0].error : null;
    const humanError = uploadError
      ? new ErrorWithHumanMessage(
          t(uploadError.message, {
            maxFileSize: toKb(uploadError.maxFileSize),
            canUploadSize: toKb(uploadError.canUploadSize),
          }),
        )
      : null;
    const untranslatedError = humanError
      ? humanError.message?.split(' [')[0]
      : null;
    return (
      <div className="row text-center">
        {!imageUrl && (
          <WebCam
            containerElement="div"
            play={playCamera}
            takePhoto={!videoMode}
            takePicture={this.takePicture}
            onError={this.handleCameraError}
          />
        )}
        {imageUrl && <img src={imageUrl} />}
        {humanError && <Alert inline={false} message={t(untranslatedError)} />}
        <div className="col-xs-12 text-center mt-15">
          <ul className="list-inline player-controls text-center heading-text">
            {videoMode && !videoError && (
              <li className="no-padding-right" title={t('Capture')}>
                <Ripple>
                  <button
                    className="btn btn-flat no-padding attachment-snap-btn text-primary-600"
                    type="button"
                    onClick={this.handleWebCamToggle}
                  >
                    <Icon name="camera" />
                  </button>
                </Ripple>
              </li>
            )}
            {!videoMode && (
              <li>
                <Ripple>
                  <button
                    className="btn btn-link btn-xs attachment-restart-video-btn"
                    type="button"
                    onClick={this.handleRetakePicture}
                  >
                    {t('Retake Picture')}
                  </button>
                </Ripple>
              </li>
            )}
          </ul>
        </div>
      </div>
    );
  }
}
