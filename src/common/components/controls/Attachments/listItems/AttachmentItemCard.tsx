import React, { FC, useCallback, useEffect, useMemo, useState } from 'react';
import uuid from 'uuid';
import { filter, forEach, get, map, maxBy } from 'lodash';

import useT from '../../../utils/Translations/useT';
import Dropdown from '../../Dropdown';
import IAttachmentItemComponentProps from '../abstract/IAttachmentItemComponentProps';

import styles from './AttachmentItemCard.scss';
import TextField from '../../../containers/EntityForm/fields/TextField';
import FilePreview from '../../../../../common/components/controls/UploadFileDialog/FilePreview';
import { DEFAULT_WRAPPER_CLASS_NAMES } from '../../../containers/EntityForm/internal/EntityFieldContent';
import FileInfoModal from '../../../../../modules/EContent/EContentLibraries/EContentLibraryItems/form/tabs/EContentItemContentsTab/form/FileInfoModal';
import Popover from '../../../utils/Popover';
import { MANDATORY, TRuleType } from '../../../../../model/RuleType';
import useEntityFormContext from '../../../containers/EntityForm/internal/useEntityFormContext';
import TagInputField from '../../../containers/EntityForm/fields/TagInputField';
import NumberField from '../../../containers/EntityForm/fields/NumberField';
import { ONE } from '../../../../const';
import SimpleValueView from '../../ValueViews/SimpleValueView';
import SplitAudioModal from './SplitAudioModal';
import ImageAiImprovementModal from './ImageAiImprovementModal';
import { IFileAttachmentTemp } from '../../../../abstract/IFileAttachments';
import { IEContentResourceCategory } from '../../../../abstract/EContent/IEContentResourceXAttribute';
import SelectBoxField from '../../../containers/EntityForm/fields/SelectBoxField';

const DEFAULT_MAX_DESCRIPTION_LENGTH = 70;
const DEFAULT_COLUMNS = 2;
const MAXIMUM = 2000000000;

const AttachmentItemCard: FC<
  IAttachmentItemComponentProps & {
    hasCaption?: boolean;
    name: string;
    documentCaptionRule: TRuleType;
    hasKeyword?: boolean;
    hasSequence?: boolean;
    maxTagLength?: number;
    isKeywordRequired?: boolean;
    suggestions?: string[];
    hasCrop?: boolean;
    hideFilename?: boolean;
    caption?: string;
    onSplitAudio?: (
      attachment: IFileAttachmentTemp,
      splittedFiles: string[],
    ) => Promise<void>;
    isImageAiImprovementEnabled?: boolean;
    onImageAiImprovement?: (
      attachment: IFileAttachmentTemp,
      image: string,
    ) => Promise<void>;
    isContentCategoryEnabled: boolean;
    resourceCategoriesOptions: IEContentResourceCategory[];
    selectedTabId?: number | string;
  }
> = ({
  dropdownOptions = [],
  attachment,
  editable,
  maxDescriptionLength = DEFAULT_MAX_DESCRIPTION_LENGTH,
  name,
  index = 0,
  columns = DEFAULT_COLUMNS,
  hasCaption,
  documentCaptionRule,
  hasKeyword = false,
  hasSequence = false,
  maxTagLength,
  isKeywordRequired,
  suggestions,
  isDownloadable,
  hasCrop,
  hideFilename,
  caption,
  onSplitAudio,
  isImageAiImprovementEnabled,
  onImageAiImprovement,
  isContentCategoryEnabled,
  resourceCategoriesOptions,
  selectedTabId,
}) => {
  const t = useT();

  const { errors, values, setFieldValue } = useEntityFormContext();

  const [showSplitModal, setShowSplitModal] = useState<boolean>(false);
  const openSplitModal = useCallback(() => {
    setShowSplitModal(true);
  }, []);
  const onSubmitSplitAudio = useCallback(
    async (attachment, splittedFiles) => {
      onSplitAudio && (await onSplitAudio(attachment, splittedFiles));
    },
    [onSplitAudio],
  );
  const onImageAiImprovementFn = useCallback(
    async image =>
      onImageAiImprovement && (await onImageAiImprovement(attachment, image)),
    [attachment, onImageAiImprovement],
  );
  const [
    showImageAiImprovementModal,
    setShowImageAiImprovementModal,
  ] = useState<boolean>(false);
  const openImageAiImprovementModal = useCallback(() => {
    setShowImageAiImprovementModal(true);
  }, []);

  const getMaxGroupSequence = useCallback((): number => {
    const list = get(values, `${name}`, []);
    const maxElement = maxBy<{ groupSequence: number }>(list, 'groupSequence');
    const maxSequence = maxElement?.groupSequence || 0;
    return maxSequence;
  }, [values]);

  useEffect(() => {
    if (hasSequence) {
      let maxSequence = ONE + getMaxGroupSequence();
      const list = get(values, `${name}`, []);
      map(list, (item, key) => {
        !item.groupSequence &&
          setFieldValue(
            `${name}.${key}.groupSequence`,
            maxSequence > MAXIMUM ? MAXIMUM : maxSequence++,
          );
      });
    }
  }, [values, hasSequence]);

  const error = get(errors, ['name', index, 'description'], '');

  const [infoData, setInfoData] = useState(null);

  const hideInfoModal = useCallback(() => setInfoData(null), [setInfoData]);

  const isCaptionRequired = useMemo(
    () => documentCaptionRule === MANDATORY.value,
    [documentCaptionRule],
  );

  const validate = useCallback(
    text =>
      text && text.length > maxDescriptionLength
        ? `Max length is ${maxDescriptionLength}`
        : '',
    [maxDescriptionLength],
  );

  const constraints = useMemo(
    () => ({
      length: {
        maximum: maxDescriptionLength,
        tooLong: t('Max length is #{maxDescriptionLength}', {
          maxDescriptionLength,
        }),
      },
    }),
    [t, maxDescriptionLength],
  );

  const anchorId = useMemo(() => `popover_${uuid.v4()}`, []);

  const title = useMemo(() => decodeURI(attachment?.fileName || ''), [
    attachment,
  ]);

  const handleDownload = useCallback(() => {
    const link = document.createElement('a');
    {
      link.href = attachment.url as string;
    }
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }, [attachment]);

  const isUploaded = useMemo(() => !!attachment?.id, [attachment]);

  const _dropdownOptions = useMemo(() => {
    const options = [
      {
        mimeType: 'all',
        label: t('Download'),
        icon: 'download8',
        onClick: handleDownload,
      },
      {
        mimeType: 'all',
        label: t('File Info'),
        icon: 'file-eye',
        onClick: setInfoData,
      },
    ];
    const isAudio = attachment?.mimeType?.indexOf('audio') > -1;
    if (isAudio && editable && onSplitAudio) {
      options.push({
        mimeType: 'audio',
        label: t('Split Audio'),
        icon: 'scissors',
        onClick: openSplitModal,
      });
    }
    if (isImageAiImprovementEnabled && isUploaded) {
      options.push({
        mimeType: 'image',
        label: t('Image AI Improvement'),
        icon: 'image-compare',
        onClick: openImageAiImprovementModal,
      });
    }

    return editable ? [...options, ...dropdownOptions] : options;
  }, [
    attachment,
    editable,
    dropdownOptions,
    t,
    openSplitModal,
    onSplitAudio,
    handleDownload,
    isImageAiImprovementEnabled,
    openImageAiImprovementModal,
    isUploaded,
  ]);

  if (selectedTabId) {
    const resourceCategoryId = get(
      values,
      `${name}[${index}].resourceCategoryId`,
    );
    if (selectedTabId === 'no-category') {
      if (resourceCategoryId !== null && resourceCategoryId !== '') {
        return <></>;
      }
    } else if (
      selectedTabId !== 'all' &&
      selectedTabId !== resourceCategoryId
    ) {
      return <></>;
    }
  }
  return (
    <div className={DEFAULT_WRAPPER_CLASS_NAMES[columns]}>
      {hasSequence ? (
        <NumberField
          required
          columns={0}
          label={t('Sequence')}
          min={1}
          name={`${name}[${index}].groupSequence`}
        />
      ) : null}
      {isContentCategoryEnabled ? (
        <SelectBoxField
          columns={0}
          itemTitlePropName="name"
          itemValuePropName="id"
          label={t('Content Category')}
          name={`${name}[${index}].resourceCategoryId`}
          options={resourceCategoriesOptions}
          returnType="number"
          withTitle={false}
        />
      ) : null}
      <div className={styles.wrapper}>
        <div className={styles.card}>
          <FilePreview
            file={attachment}
            hasCrop={hasCrop}
            hasRemove={false}
            hideFilename={hideFilename}
            onDownload={isDownloadable ? handleDownload : undefined}
          />

          <div className={styles.fileName} title={title}>
            {title}
          </div>

          <div className={styles.actions} id={anchorId}>
            <Dropdown
              actions={_dropdownOptions}
              data={attachment}
              position="right"
            />
          </div>
        </div>
        {hasCaption && !caption ? (
          <TextField
            hasStrictLengthLimit
            noLabel
            className={styles.caption}
            columns={0}
            constraints={constraints}
            maxLength={maxDescriptionLength}
            name={`${name}[${index}].description`}
            placeholder={t('Caption')}
            required={isCaptionRequired}
            validate={validate}
          />
        ) : null}
        {hasCaption && caption ? (
          <SimpleValueView
            noLabel
            className={styles.caption}
            isMultiline={false}
            label=""
            value={caption}
          />
        ) : null}
      </div>
      {hasKeyword ? (
        <TagInputField
          columns={0}
          errorMessage={error}
          label={t('Content Keyword')}
          maxTagLength={maxTagLength}
          name={`${name}[${index}].contentKeyword`}
          required={isKeywordRequired}
          suggestions={suggestions}
        />
      ) : null}
      <Popover
        key={anchorId}
        isCloseButton
        anchorId={anchorId}
        contentWrapperClassName={styles.popover}
        position="responsive"
        visible={!!infoData}
        onClose={hideInfoModal}
      >
        <FileInfoModal item={infoData} />
      </Popover>
      <SplitAudioModal
        attachment={attachment}
        isModalVisible={showSplitModal}
        setModalVisibility={setShowSplitModal}
        onSubmit={onSubmitSplitAudio}
      />
      <ImageAiImprovementModal
        attachment={attachment}
        isModalVisible={showImageAiImprovementModal}
        setModalVisibility={setShowImageAiImprovementModal}
        onSubmit={onImageAiImprovementFn}
      />
    </div>
  );
};

export default AttachmentItemCard;
