import React, { useCallback, useMemo, useState } from 'react';
import { find, map, isNil, differenceWith, isEmpty } from 'lodash';
import { useLocation, useRouteMatch } from 'react-router-dom';
import { DocumentNode } from 'graphql';
import classNames from 'classnames';
import { useMutation } from 'react-apollo';

import eContentItems from '../../../../../../../../common/data/eContent/eContentItems.graphql';
import eContentItemsCount from '../../../../../../../../common/data/eContent/eContentItemsCount.graphql';
import cookColumns from './tableColumns';
import IEContentResource from '../../../../../../../abstract/EContent/IEContentResource';
import { TEContentLibraryResourcesTreeTypes } from '../../../../../../../../model/EContentLibraryResourcesTypeNames';
import useT from '../../../../../../utils/Translations/useT';
import GqlFullCrudTable from '../../../../../../dataViews/GqlFullCrudTable';
import IEContentItem from '../../../../../../../abstract/EContent/IEContentItem';
import { Active } from '../../../../../../../../model/StatusWithDraft';
import ILanguage from '../../../../../../../abstract/ILanguage';
import Checkbox from '../../../../../../controls/base/Checkbox';
import EContentLibrariesResourcesTreeSelector from '../../../../../../controls/FilterBar/EContentLibrariesResourcesTreeSelector';
import LanguagesMultiSelector from '../../../../../../controls/FilterBar/LanguagesMultiSelector';
import StatusWithDraftMultiSelector from '../../../../../../controls/FilterBar/StatusWithDraftMultiSelector';
import IEContentLibrary from '../../../../../../../abstract/EContent/IEContentLibrary';
import cookColumnsView from './tableColumnsView';
import { IDropdownAction } from '../../../../../../controls/Dropdown';
import { redirectNewTab } from '../../../../../../../utils/urlHelper';
import { TIcon } from '../../../../../../../propTypes';
import EContentLibrariesResourcesWithIdsTreeSelector from '../../../../../../controls/FilterBar/EContentLibrariesResourcesWithIdsTreeSelector';
import swal2 from '../../../../../../../utils/swal2';
import styles from './ItemsView.scss';
import StatusWithDraftAndActiveMultiSelector from '../../../../../../controls/FilterBar/StatusWithDraftAndActiveMultiSelector';
import Notifications from '../../../../../../../utils/Notifications';
import moveTaskXEcnSequence from '../data/moveTaskXEcnSequence.graphql';
import useFilterStore from '../../../../../../controls/FilterBar/hooks/useFilterStore';

export interface IItemsView {
  handleDeleteItem?: (id: number) => Promise<void>;
  query?: DocumentNode;
  countQuery?: DocumentNode;
  selectedResources?: Partial<IEContentResource>[];
  libraryId?: number;
  libraryName?: string;
  usedKeywords?: string[];
  isListItemQueryPrevented(variables: object): boolean;
  goToCreate(): void;
  cookQueryVariables: Function;
  rightActionButtons: ((props: object) => JSX.Element) | JSX.Element | null;
  onFilterChange: (filter: object) => void;
  cookFilterModels: (
    items: TEContentLibraryResourcesTreeTypes[],
  ) => TEContentLibraryResourcesTreeTypes[];
  filterLanguages: (options: ILanguage[], values: object) => ILanguage[];
  parentUrl?: string;
  selectedContentItems?: IEContentItem[];
  setSelectedContentItems?: React.Dispatch<
    React.SetStateAction<IEContentItem[]>
  >;
  organisationGroupId: number;
  libraries: IEContentLibrary[];
  libraryLoading: boolean;
  withLibraryFilter?: boolean;
  resources: any;
  inModal?: boolean;
  isLsLpTask?: boolean;
  isAllSelected?: boolean;
  setIsAllSelected?: React.Dispatch<React.SetStateAction<boolean>>;
  taskId?: number;
}

const ItemsView: React.FC<IItemsView> = ({
  resources,
  handleDeleteItem,
  query,
  countQuery,
  selectedResources,
  isListItemQueryPrevented,
  cookQueryVariables,
  onFilterChange,
  filterLanguages,
  selectedContentItems,
  setSelectedContentItems,
  organisationGroupId,
  libraries,
  libraryLoading,
  withLibraryFilter = true,
  inModal = false,
  isLsLpTask = false,
  isAllSelected,
  setIsAllSelected,
  taskId,
}) => {
  const t = useT();
  const location = useLocation();
  const inMySpace = useMemo(() => location.pathname.startsWith('/my-space'), [
    location,
  ]);
  const { url } = useRouteMatch();
  const [listItems, setListItems] = useState<IEContentItem[]>([]);

  const {
    loading: fLoading,
    values: defaultFilterValues = {
      resources: selectedResources,
      status: [Active.value],
      languageId: null,
    },
    onChange: onDefaultFilterValuesChange,
  } = useFilterStore('TASK_ITEM_VIEW_FILTERS', {
    defaultValues: {
      resources: selectedResources,
      status: [Active.value],
      languageId: null,
    },
  });

  const [resourceTreeLoading, setResourceTreeLoading] = useState<boolean>(true);

  const onItemSelect = useCallback(
    (item: IEContentItem) => {
      const exists =
        selectedContentItems?.length &&
        find(selectedContentItems, { id: item?.id });
      if (exists) {
        const filtered = selectedContentItems?.filter(x => x.id !== item?.id);
        setSelectedContentItems && setSelectedContentItems(filtered || []);
        setIsAllSelected &&
          setIsAllSelected(!!(filtered?.length === listItems?.length));
      } else {
        const all = [...(selectedContentItems as IEContentItem[]), item];
        setSelectedContentItems && setSelectedContentItems(all);
        setIsAllSelected &&
          setIsAllSelected(!!(all?.length === listItems?.length));
      }
    },
    [
      setSelectedContentItems,
      selectedContentItems,
      listItems,
      setIsAllSelected,
    ],
  );

  const handleCookItems = useCallback(items => {
    setListItems(items);
    return items;
  }, []);

  const _onToggleTickAll = useCallback(() => {
    const differencesById = differenceWith(
      selectedContentItems as IEContentItem[],
      listItems as IEContentItem[],
      (a, b) => a.id === b.id,
    );
    if (!isAllSelected) {
      setSelectedContentItems &&
        setSelectedContentItems([
          ...(selectedContentItems as IEContentItem[]),
          ...listItems,
        ]);
    } else if (isAllSelected && !isEmpty(differencesById)) {
      setSelectedContentItems && setSelectedContentItems(differencesById);
    } else if (isAllSelected && isEmpty(differencesById)) {
      setSelectedContentItems && setSelectedContentItems([]);
    }
    setIsAllSelected && setIsAllSelected(!isAllSelected);
  }, [
    isAllSelected,
    setSelectedContentItems,
    listItems,
    selectedContentItems,
    setIsAllSelected,
  ]);

  const onTickAllCheckbox = useMemo(
    () => <Checkbox value={isAllSelected} onChange={_onToggleTickAll} />,
    [isAllSelected, _onToggleTickAll],
  );
  const columns = useMemo(
    () =>
      selectedContentItems && setSelectedContentItems
        ? cookColumns(
            t,
            onItemSelect,
            onTickAllCheckbox,
            selectedContentItems,
            !!isAllSelected,
          )
        : cookColumnsView(t, libraries),
    [
      libraries,
      t,
      onItemSelect,
      onTickAllCheckbox,
      selectedContentItems,
      isAllSelected,
      setSelectedContentItems,
    ],
  );
  const [filter, setFilter] = useState();

  const languageIds = useMemo(
    () => [
      ...new Set(
        map(selectedResources, 'languageIds').flat().filter(Number.isFinite),
      ),
    ],
    [selectedResources],
  );

  const resetSelection = useCallback(() => {
    setIsAllSelected && setIsAllSelected(false);
  }, [setIsAllSelected]);

  const handleFilterChange = useCallback(
    filter => {
      resetSelection();
      setFilter(filter);
      onFilterChange(filter);
      onDefaultFilterValuesChange({
        resources: filter.resources,
        languageId: filter.languageId,
        status: filter.status,
      });
    },
    [setFilter, onFilterChange, resetSelection, onDefaultFilterValuesChange],
  );

  const eContentLibrariesResourcesTreeSelector = useCallback(
    props => {
      const _onChange = value => {
        handleFilterChange({
          count: props.filter.count,
          languageId: props.filter.languageId,
          searchQuery: props.filter.searchQuery,
          status: props.filter.status,
          resources: value,
        });
        props.onChange(value);
      };
      return (
        <>
          {withLibraryFilter ? (
            <EContentLibrariesResourcesTreeSelector
              {...props}
              libraries={libraries}
              libraryLoading={libraryLoading}
              organisationGroupId={organisationGroupId}
              resources={resources}
            />
          ) : (
            <EContentLibrariesResourcesWithIdsTreeSelector
              {...props}
              organisationGroupId={organisationGroupId}
              resources={resources}
              setResourceTreeLoading={setResourceTreeLoading}
              onChange={_onChange}
            />
          )}
        </>
      );
    },
    [
      organisationGroupId,
      libraries,
      libraryLoading,
      resources,
      withLibraryFilter,
      handleFilterChange,
      setResourceTreeLoading,
    ],
  );

  const languagesMultiSelector = useCallback(
    props => (
      <LanguagesMultiSelector
        {...props}
        isEmptyAllowed
        filterOptions={filterLanguages}
        value={isNil(props?.value) && languageIds ? languageIds : props?.value}
      />
    ),
    [filterLanguages, languageIds],
  );

  const statusWithDraftMultiSelector = useCallback(
    props =>
      inModal ? (
        <StatusWithDraftAndActiveMultiSelector {...props} hasAllOption />
      ) : (
        <StatusWithDraftMultiSelector {...props} hasAllOption />
      ),
    [inModal],
  );

  const openItem = useCallback((row: IEContentItem) => {
    const link = `/e-content/libraries/edit/${row.resource.libraryId}/edit/${row.id}/contents-list`;
    redirectNewTab(link);
  }, []);

  const _onDelete = useCallback(
    async (row: IEContentItem) => {
      const { isConfirmed } = await swal2({
        title: t("Remove '#{name}'?", {
          name: row.name,
        }),
        text: t(`Are you sure you want to remove this item?`),
        icon: 'warning',
        deleteMode: true,
        confirmButtonText: t('Remove'),
        cancelButtonText: t('Cancel'),
        className: classNames(styles.modalWrapper),
      });
      if (isConfirmed) {
        handleDeleteItem && handleDeleteItem(row.id as number);
      }
    },
    [t, handleDeleteItem],
  );

  const additionalOptionsList = useCallback<
    (row: IEContentItem) => IDropdownAction[]
  >(
    row =>
      isLsLpTask || !inMySpace
        ? [
            {
              icon: 'new-tab' as TIcon,
              label: t('Open'),
              onClick: event => {
                event && event.preventDefault();
                openItem(row);
              },
            },
            {
              icon: 'ed-remove',
              iconType: 'ed',
              label: t('Remove'),
              onClick: () => _onDelete(row),
            },
          ]
        : [
            {
              icon: 'new-tab' as TIcon,
              label: t('Open'),
              onClick: event => {
                event && event.preventDefault();
                openItem(row);
              },
            },
          ],
    [t, openItem, _onDelete, isLsLpTask, inMySpace],
  );

  const onRowClick = useCallback(
    row => {
      !inModal && openItem(row);
    },
    [inModal, openItem],
  );

  const [_moveTaskXEcnSequence, { loading: isSequenceMoving }] = useMutation(
    moveTaskXEcnSequence,
  );

  const onMoveSequence = useCallback(
    async (_sequenceFrom: number, _sequenceTo: number) => {
      const sequenceFrom = listItems[_sequenceFrom]?.sequence as number;
      const sequenceTo = listItems[_sequenceTo].sequence;

      await _moveTaskXEcnSequence({
        variables: {
          tableName: isLsLpTask
            ? 'PG_LS_LP_TASK_X_ECN_ITEM'
            : 'PG_LP_TASK_X_ECN_ITEM',
          [isLsLpTask ? 'lsLpTaskId' : 'lpTaskId']: taskId,
          sequenceFrom,
          sequenceTo,
        },
      });
      Notifications.success(t('Updated Succesfully'), '', t);
      return true;
    },
    [listItems, t, _moveTaskXEcnSequence, taskId, isLsLpTask],
  );

  const onDragSortUpdate = useCallback(
    async (initialIndex, finalIndex) => {
      if (initialIndex === finalIndex) {
        return false;
      }

      await onMoveSequence(initialIndex, finalIndex);
      return true;
    },
    [onMoveSequence],
  );

  return (
    <GqlFullCrudTable<
      IEContentItem,
      {
        libraryId?: number;
        libraryName?: string;
        selectedResources?: Partial<IEContentResource>[];
        goToCreateItem: () => void;
        usedKeywords?: string[];
      }
    >
      hasFilters
      gql={{
        query: query || eContentItems,
        count: countQuery || eContentItemsCount,
        cookQueryVariables,
        isListItemQueryPrevented,
      }}
      isLoading={isSequenceMoving || fLoading}
      list={{
        onRowClick,
        hasPaginationRouting: false,
        hasClearStyles: true,
        canDelete,
        onFilterChange: handleFilterChange,
        cookItems: handleCookItems,
        filterComponent: {
          resources: eContentLibrariesResourcesTreeSelector,
          languageId: languagesMultiSelector,
          status: statusWithDraftMultiSelector,
        },
        initialFilter: {
          status: defaultFilterValues.status,
          languageId: defaultFilterValues.languageId,
          searchQuery: '',
          resources: defaultFilterValues.resources,
        },
        tableConfig: {
          columns,
          additionalOptionsList,
          isDraggable: !inModal,
          isDraggableHover: !inModal,
          onDragSortUpdate,
        },
      }}
      panelClassName="no-padding"
      title=""
    />
  );
};

export default ItemsView;

const canDelete = () => false;
