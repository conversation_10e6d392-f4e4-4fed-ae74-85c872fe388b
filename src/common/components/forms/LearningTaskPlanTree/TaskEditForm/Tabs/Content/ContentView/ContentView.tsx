import React, { useCallback, useMemo, useState } from 'react';
import classNames from 'classnames';
import { useLocation } from 'react-router-dom';
import {
  differenceWith,
  filter,
  find,
  isEmpty,
  isNil,
  map,
  some,
  uniq,
} from 'lodash';
import { DocumentNode } from 'graphql';
import { useMutation } from 'react-apollo';
import eContentContentLsView from '../../../../../../../../common/data/eContent/eContentContentLsView.graphql';
import eContentContentLsViewCount from '../../../../../../../../common/data/eContent/eContentContentLsViewCount.graphql';
import IEContentResource from '../../../../../../../abstract/EContent/IEContentResource';
import { TEContentLibraryResourcesTreeTypes } from '../../../../../../../../model/EContentLibraryResourcesTypeNames';
import useT from '../../../../../../utils/Translations/useT';
import GqlFullCrudTable from '../../../../../../dataViews/GqlFullCrudTable';
import IEContentContent from '../../../../../../../abstract/EContent/IEContentContent';
import { Active } from '../../../../../../../../model/StatusWithDraft';
import ILanguage from '../../../../../../../abstract/ILanguage';
import Checkbox from '../../../../../../controls/base/Checkbox';
import EContentLibrariesResourcesTreeSelector from '../../../../../../controls/FilterBar/EContentLibrariesResourcesTreeSelector';
import LanguagesMultiSelector from '../../../../../../controls/FilterBar/LanguagesMultiSelector';
import StatusWithDraftMultiSelector from '../../../../../../controls/FilterBar/StatusWithDraftMultiSelector';
import IEContentLibrary from '../../../../../../../abstract/EContent/IEContentLibrary';
import { IDropdownAction } from '../../../../../../controls/Dropdown';
import { redirectNewTab } from '../../../../../../../utils/urlHelper';
import { TIcon } from '../../../../../../../propTypes';
import cookColumns from './tableColumns';
import cookColumnsView from './tableColumnsView';
import EContentLibrariesResourcesWithIdsTreeSelector from '../../../../../../controls/FilterBar/EContentLibrariesResourcesWithIdsTreeSelector';
import EcnItemMultiSelector from '../../../../../../controls/FilterBar/EcnItemMultiSelector';
import swal2 from '../../../../../../../utils/swal2';
import styles from './ContentView.scss';
import { cookEContentClobs } from '../../../../../../../../modules/EContent/EContentLibraries/EContentLibraryItems/form/tabs/EContentItemContentsTab/form/EContentItemContentForm';
import StatusWithDraftAndActiveMultiSelector from '../../../../../../controls/FilterBar/StatusWithDraftAndActiveMultiSelector';
import Notifications from '../../../../../../../utils/Notifications';
import moveTaskXEcnSequence from '../data/moveTaskXEcnSequence.graphql';
import useFilterStore from '../../../../../../controls/FilterBar/hooks/useFilterStore';

export interface IContentsView {
  handleDeleteItem?: (id: number) => Promise<void>;
  query?: DocumentNode;
  countQuery?: DocumentNode;
  selectedResources?: Partial<IEContentResource>[];
  libraryId?: number;
  libraryName?: string;
  usedKeywords?: string[];
  isListItemQueryPrevented(variables: object): boolean;
  goToCreate(): void;
  cookQueryVariables: Function;
  rightActionButtons: ((props: object) => JSX.Element) | JSX.Element | null;
  onFilterChange: (filter: object) => void;
  cookFilterModels: (
    items: TEContentLibraryResourcesTreeTypes[],
  ) => TEContentLibraryResourcesTreeTypes[];
  filterLanguages: (options: ILanguage[], values: object) => ILanguage[];
  parentUrl?: string;
  selectedContentItems?: IEContentContent[];
  setSelectedContentItems?: React.Dispatch<
    React.SetStateAction<IEContentContent[]>
  >;
  organisationGroupId: number;
  libraries: IEContentLibrary[];
  libraryLoading: boolean;
  resources: any;
  withLibraryFilter?: boolean;
  inModal?: boolean;
  ecnItems: any[];
  isLsLpTask?: boolean;
  isAllSelected?: boolean;
  setIsAllSelected?: React.Dispatch<React.SetStateAction<boolean>>;
  taskId?: number;
}

const ContentView: React.FC<IContentsView> = ({
  resources,
  handleDeleteItem,
  query,
  countQuery,
  selectedResources,
  isListItemQueryPrevented,
  cookQueryVariables,
  onFilterChange,
  filterLanguages,
  selectedContentItems,
  setSelectedContentItems,
  organisationGroupId,
  libraries,
  libraryLoading,
  withLibraryFilter = true,
  inModal = false,
  ecnItems,
  isLsLpTask = false,
  isAllSelected,
  setIsAllSelected,
  taskId,
}) => {
  const t = useT();
  const location = useLocation();
  const inMySpace = useMemo(() => location.pathname.startsWith('/my-space'), [
    location,
  ]);

  const {
    loading: fLoading,
    values: defaultFilterValues = {
      resources: selectedResources,
      status: [Active.value],
      ecnItemId: null,
      languageId: null,
    },
    onChange: onDefaultFilterValuesChange,
  } = useFilterStore('TASK_CONTENT_VIEW_FILTERS', {
    defaultValues: {
      resources: selectedResources,
      status: [Active.value],
      ecnItemId: null,
      languageId: null,
    },
  });

  const [resourceTreeLoading, setResourceTreeLoading] = useState<boolean>(true);

  const ecnItemOptions = useMemo(
    () =>
      uniq(
        filter(ecnItems, item1 =>
          some(selectedResources, item2 => item1.item.resourceId === item2.id),
        ).map(x => x.item),
      ),
    [ecnItems, selectedResources],
  );

  const resetSelection = useCallback(() => {
    setIsAllSelected && setIsAllSelected(false);
  }, [setIsAllSelected]);

  const handleFilterChange = useCallback(
    filter => {
      resetSelection();
      onFilterChange(filter);
      onDefaultFilterValuesChange({
        resources: filter.resources,
        ecnItemId: filter.ecnItemId,
        languageId: filter.languageId,
        status: filter.status,
      });
    },
    [onFilterChange, resetSelection, onDefaultFilterValuesChange],
  );
  const [listItems, setListItems] = useState<IEContentContent[]>([]);
  const onItemSelect = useCallback(
    (item: IEContentContent) => {
      const exists =
        selectedContentItems?.length &&
        find(selectedContentItems, { id: item?.id });
      if (exists) {
        const filtered = selectedContentItems?.filter(x => x.id !== item?.id);
        setSelectedContentItems && setSelectedContentItems(filtered || []);
        setIsAllSelected &&
          setIsAllSelected(!!(filtered?.length === listItems?.length));
      } else {
        setSelectedContentItems &&
          setSelectedContentItems(prev => [...prev, item]);
      }
    },
    [
      setSelectedContentItems,
      selectedContentItems,
      listItems,
      setIsAllSelected,
    ],
  );

  const handleCookItems = useCallback(items => {
    setListItems(items);
    return items;
  }, []);

  const _onToggleTickAll = useCallback(() => {
    const differencesById = differenceWith(
      selectedContentItems as IEContentContent[],
      listItems as IEContentContent[],
      (a, b) => a.id === b.id,
    );
    if (!isAllSelected) {
      setSelectedContentItems &&
        setSelectedContentItems([
          ...(selectedContentItems as IEContentContent[]),
          ...listItems,
        ]);
    } else if (isAllSelected && !isEmpty(differencesById)) {
      setSelectedContentItems && setSelectedContentItems(differencesById);
    } else if (isAllSelected && isEmpty(differencesById)) {
      setSelectedContentItems && setSelectedContentItems([]);
    }
    setIsAllSelected && setIsAllSelected(!isAllSelected);
  }, [
    isAllSelected,
    setSelectedContentItems,
    listItems,
    selectedContentItems,
    setIsAllSelected,
  ]);

  const onTickAllCheckbox = useMemo(
    () => <Checkbox value={isAllSelected} onChange={_onToggleTickAll} />,
    [isAllSelected, _onToggleTickAll],
  );

  const columns = useMemo(
    () =>
      selectedContentItems && setSelectedContentItems
        ? cookColumns(
            t,
            onItemSelect,
            onTickAllCheckbox,
            selectedContentItems,
            !!isAllSelected,
          )
        : cookColumnsView(t, libraries),
    [
      t,
      onItemSelect,
      onTickAllCheckbox,
      selectedContentItems,
      isAllSelected,
      setSelectedContentItems,
      libraries,
    ],
  );

  const languageIds = useMemo(
    () => [
      ...new Set(
        map(selectedResources, 'languageIds').flat().filter(Number.isFinite),
      ),
    ],
    [selectedResources],
  );

  const eContentLibrariesResourcesTreeSelector = useCallback(
    props => {
      const _onChange = value => {
        handleFilterChange({
          count: props.filter.count,
          languageId: props.filter.languageId,
          searchQuery: props.filter.searchQuery,
          status: props.filter.status,
          resources: value,
        });
        props.onChange(value);
      };
      return (
        <>
          {withLibraryFilter ? (
            <EContentLibrariesResourcesTreeSelector
              {...props}
              libraries={libraries}
              libraryLoading={libraryLoading}
              organisationGroupId={organisationGroupId}
              resources={resources}
            />
          ) : (
            <EContentLibrariesResourcesWithIdsTreeSelector
              {...props}
              organisationGroupId={organisationGroupId}
              resources={resources}
              setResourceTreeLoading={setResourceTreeLoading}
              onChange={_onChange}
            />
          )}
        </>
      );
    },
    [
      organisationGroupId,
      libraries,
      libraryLoading,
      resources,
      withLibraryFilter,
      handleFilterChange,
      setResourceTreeLoading,
    ],
  );

  const ecnItemMultiSelector = useCallback(
    props => (
      <EcnItemMultiSelector
        {...props}
        isEmptyAllowed
        inModal={inModal}
        options={ecnItemOptions}
        resourceIds={uniq(map(props?.filter.resources, 'id')) || []}
        value={
          isNil(props?.value) && !isEmpty(ecnItemOptions) && !inModal
            ? map(ecnItemOptions, 'id').flat().filter(Number.isFinite)
            : props?.value
        }
      />
    ),
    [ecnItemOptions, inModal],
  );

  const languagesMultiSelector = useCallback(
    props => (
      <LanguagesMultiSelector
        {...props}
        isEmptyAllowed
        filterOptions={filterLanguages}
        value={isNil(props?.value) && languageIds ? languageIds : props?.value}
      />
    ),

    [filterLanguages, languageIds],
  );

  const statusWithDraftMultiSelector = useCallback(
    props =>
      inModal ? (
        <StatusWithDraftAndActiveMultiSelector {...props} hasAllOption />
      ) : (
        <StatusWithDraftMultiSelector {...props} hasAllOption />
      ),
    [inModal],
  );

  const openItem = useCallback((row: IEContentContent) => {
    const link = `/e-content/libraries/edit/${row.item.resource.libraryId}/edit/${row.item.id}/contents-list/edit/${row.id}/content`;
    redirectNewTab(link);
  }, []);

  const _onDelete = useCallback(
    async (row: IEContentContent) => {
      const name = !row.clobs
        ? 'item'
        : cookEContentClobs(row.clobs, row.item.resource?.attributes)?.heading
            ?.content || 'item';
      const { isConfirmed } = await swal2({
        title: t("Remove '#{name}'?", {
          name,
        }),
        text: t(`Are you sure you want to remove this content?`),
        icon: 'warning',
        deleteMode: true,
        confirmButtonText: t('Remove'),
        cancelButtonText: t('Cancel'),
        className: classNames(styles.modalWrapper),
      });
      if (isConfirmed) {
        handleDeleteItem && handleDeleteItem(row.id as number);
      }
    },
    [t, handleDeleteItem],
  );

  const additionalOptionsList = useCallback<
    (row: IEContentContent) => IDropdownAction[]
  >(
    row =>
      isLsLpTask || !inMySpace
        ? [
            {
              icon: 'new-tab' as TIcon,
              label: t('Open'),
              onClick: event => {
                event && event.preventDefault();
                openItem(row);
              },
            },
            {
              icon: 'ed-remove',
              iconType: 'ed',
              label: t('Remove'),
              onClick: () => _onDelete(row),
            },
          ]
        : [
            {
              icon: 'new-tab' as TIcon,
              label: t('Open'),
              onClick: event => {
                event && event.preventDefault();
                openItem(row);
              },
            },
          ],
    [t, openItem, _onDelete, isLsLpTask, inMySpace],
  );

  const onRowClick = useCallback(
    row => {
      !inModal && openItem(row);
    },
    [inModal, openItem],
  );

  const [_moveTaskXEcnSequence, { loading: isSequenceMoving }] = useMutation(
    moveTaskXEcnSequence,
  );

  const onMoveSequence = useCallback(
    async (_sequenceFrom: number, _sequenceTo: number) => {
      const sequenceFrom = listItems[_sequenceFrom]?.sequence as number;
      const sequenceTo = listItems[_sequenceTo].sequence;

      await _moveTaskXEcnSequence({
        variables: {
          tableName: isLsLpTask
            ? 'PG_LS_LP_TASK_X_ECN_CONTENT'
            : 'PG_LP_TASK_X_ECN_CONTENT',
          [isLsLpTask ? 'lsLpTaskId' : 'lpTaskId']: taskId,
          sequenceFrom,
          sequenceTo,
        },
      });
      Notifications.success(t('Updated Succesfully'), '', t);
      return true;
    },
    [listItems, t, _moveTaskXEcnSequence, taskId, isLsLpTask],
  );

  const onDragSortUpdate = useCallback(
    async (initialIndex, finalIndex) => {
      if (initialIndex === finalIndex) {
        return false;
      }

      await onMoveSequence(initialIndex, finalIndex);
      return true;
    },
    [onMoveSequence],
  );

  return (
    <GqlFullCrudTable<
      IEContentContent,
      {
        libraryId?: number;
        libraryName?: string;
        selectedResources?: Partial<IEContentResource>[];
        goToCreateItem: () => void;
        usedKeywords?: string[];
      }
    >
      hasFilters
      hasPagination
      gql={{
        query: query || eContentContentLsView,
        count: countQuery || eContentContentLsViewCount,
        cookQueryVariables,
        isListItemQueryPrevented,
      }}
      isLoading={isSequenceMoving || fLoading}
      list={{
        onRowClick,
        hasPaginationRouting: false,
        hasClearStyles: true,
        canDelete,
        onFilterChange: handleFilterChange,
        cookItems: handleCookItems,
        filterComponent: {
          resources: eContentLibrariesResourcesTreeSelector,
          ecnItemId: ecnItemMultiSelector,
          languageId: languagesMultiSelector,
          status: statusWithDraftMultiSelector,
        },
        initialFilter: {
          status: defaultFilterValues.status,
          ecnItemId: defaultFilterValues.ecnItemId,
          languageId: defaultFilterValues.languageId,
          searchQuery: '',
          resources: defaultFilterValues.resources,
        },
        tableConfig: {
          columns,
          additionalOptionsList,
          isDraggable: !inModal,
          isDraggableHover: !inModal,
          onDragSortUpdate,
        },
      }}
      panelClassName="no-padding"
      title=""
    />
  );
};

export default ContentView;

const canDelete = () => false;
