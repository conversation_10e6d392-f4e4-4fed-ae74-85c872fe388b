import { useMemo } from 'react';
import { isBroadcastOrChannel } from '../../../../../model/MessageType';
import { Active } from '../../../../../model/Statuses';
import useIsMessageMine from './useIsMessageMine';
import IMessage from '../../../../abstract/Messages/IMessage';
import useCurrentMessage from './useCurrentMessage';

export default function useIsMessageReadonly(
  item?: IMessage | undefined,
): boolean {
  const isMessageMine = useIsMessageMine();
  const { selectedItem: selected } = useCurrentMessage();
  const selectedItem = item || selected;

  return useMemo<boolean>(
    () =>
      (!!selectedItem?.id && selectedItem?.status !== Active.value) ||
      (isBroadcastOrChannel(selectedItem) && !isMessageMine(selectedItem)),
    [selectedItem, isMessageMine],
  );
}
