import { map } from 'lodash';
import moment from 'moment';
import { useCallback, useState } from 'react';
import { useMutation } from 'react-apollo';
import { useDebounce } from 'react-use';
import IMessageItem from '../../../../abstract/Messages/IMessageItem';
import readMessageItemMutation from '../data/readMessageItem.graphql';
import useEdComSelectedItem from '../../../../../modules/EdCom/EdComCrud/context/EdComSelectedItem';
import IMessage from '../../../../abstract/Messages/IMessage';

const DEFAULT_DURATION = 1000;

export default function useReadMessageItem({
  messages,
  setMessages,
  refetchUnreadCount,
}: {
  messages: IMessageItem[];
  setMessages: (messages: IMessageItem[]) => void;
  refetchUnreadCount?: () => void;
}): {
  onRead: (item: IMessageItem) => void;
} {
  const [messageToRead, setMessageToRead] = useState<IMessageItem>();
  const { selectedItem, setSelectedItem } = useEdComSelectedItem<IMessage>();

  const [readMessageItem] = useMutation<
    { readMessageItem: IMessage },
    { id: number }
  >(readMessageItemMutation, {
    onCompleted: data => {
      if (data?.readMessageItem && refetchUnreadCount) {
        refetchUnreadCount();
        // eslint-disable-next-line @typescript-eslint/ban-ts-ignore
        // @ts-ignore
        setSelectedItem({
          ...selectedItem,
          status: data?.readMessageItem.status,
        });
      }
    },
  });

  useDebounce(
    async () => {
      if (messageToRead) {
        const {
          data: { readMessageItem: isRead } = { readMessageItem: false },
        } = await readMessageItem({
          variables: { id: messageToRead.id },
        });

        if (isRead) {
          setMessages(
            map(messages, message =>
              moment(message.createdAt).isSameOrBefore(messageToRead.createdAt)
                ? {
                    ...message,
                    isNew: false,
                  }
                : message,
            ),
          );
        }
      }
    },
    DEFAULT_DURATION,
    [messageToRead, readMessageItem],
  );

  const onRead = useCallback<(message: IMessageItem) => void>(
    message => {
      if (
        !messageToRead ||
        moment(messageToRead.createdAt).isBefore(message.createdAt)
      ) {
        setMessageToRead(message);
      }
    },
    [messageToRead],
  );

  return {
    onRead,
  };
}
