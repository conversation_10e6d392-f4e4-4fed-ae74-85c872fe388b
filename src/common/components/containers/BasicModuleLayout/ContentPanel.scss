.heading {
  display: flex !important;
  justify-content: space-between !important;
}

.title {
  display: inline-block;
  word-break: break-all;
}

.bgWhite,
.bgWhite:focus {
  background-color: #fff !important;
}

.clear {
  padding: 0;
}

.clearHeader {
  border-color: transparent !important;
}

.navigationLink {
  display: flex !important;
  align-items: flex-start;

  i {
    min-width: 20px;
    margin-right: 10px;
  }
}

:global(.sidebar-xs) {
  .navigationLink {
    justify-items: center;

    i {
      min-width: 20px;
      margin-left: auto !important;
      margin-right: auto !important;
    }

    :global(.count) {
      right: 8px;
      position: absolute;
      top: 8px;
    ;
    }
  }
}
.panelBodySpacing {
  padding-top: 0 !important;
}

.navigationLinkWithoutIcon li ul li a{
  display: flex;
  justify-content: flex-start;
  align-items: center;
  gap: 15px;
}

.navigationLinkWithoutIcon li ul li a:not(:has(> i)){
  padding-left: 58px !important;
}

.navigationLinkWithoutIcon li ul li a i{
  margin-right: 0px !important;
}

.navigationLinkWithoutIcon li ul li a span{
  width: 100%;
}