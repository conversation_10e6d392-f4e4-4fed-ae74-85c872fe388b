import React, { FC, useCallback, useMemo, useState, useEffect } from 'react';
import { filter, includes, isEmpty, get } from 'lodash';
import { useQuery } from 'react-apollo';

import query from '../../../../data/eContent/eContentLibraryResourcesTree.graphql';
import librariesQuery from '../../../../data/eContent/eContentLibraries.graphql';
import {
  isEContentLibraryFolder,
  resolveNodeClassName,
  resolveNodeId,
  resolveNodeParentId,
  TEContentLibraryResourcesTreeTypes,
  canRender,
} from '../../../../../model/EContentLibraryResourcesTypeNames';
import useT from '../../../utils/Translations/useT';
import { isRootNode, SelectionMode } from '../../../dataViews/DynamicTree';
import TreeSelectorField from './TreeSelectorField';
import nodeSortingOptionsSequence from '../../../../utils/nodeSortingOptionsSequence';
import { TreeSelectorFieldContent } from './TreeSelectorField/TreeSelectorFieldContent';
import StatusWithDraft, {
  Active,
  Draft,
  Deleted,
} from '../../../../../model/StatusWithDraft';
import { catsearch } from '../../../../../common/textSearch';
import useCurrentUser from '../../../../data/hooks/useCurrentUser';
import FilterBar from '../../../controls/FilterBar';
import SearchField from '../../../controls/base/SearchField';
import IEContentLibrary from '../../../../abstract/EContent/IEContentLibrary';

import StatusAddon from '../../../dataViews/NextTree/addons/StatusAddon';
import ActionsAddon, {
  IActionsAddonAction,
} from '../../../dataViews/NextTree/addons/ActionsAddon/ActionsAddon';

interface ILibraryFilterValue {
  library?: IEContentLibrary | null;
  searchQuery?: string;
}

export interface IEContentLibraryResourcesTreeSelectorField {
  selectionFilter?: (
    items: TEContentLibraryResourcesTreeTypes[],
  ) => TEContentLibraryResourcesTreeTypes[];
  cookModels?: (
    items: TEContentLibraryResourcesTreeTypes[],
  ) => TEContentLibraryResourcesTreeTypes[];
  libraryId?: number;
  syntheticRootNodeName?: string;
  title?: string;
  name?: string;
  label?: string;
  isRequired?: boolean;
  isDisabled?: boolean;
  columns?: number;
  nodeIsSelectable?: (item: TEContentLibraryResourcesTreeTypes) => boolean;
  gqlVariables?: object;
  filter?: TreeSelectorFieldContent<TEContentLibraryResourcesTreeTypes>['filter'];
  excludeId?: number;
  canRenderAdapter?: boolean;
  enableLibraryFilter?: boolean;
}
const EContentLibraryResourcesTreeSelectorField: FC<IEContentLibraryResourcesTreeSelectorField> = ({
  libraryId,
  syntheticRootNodeName,
  name = 'parentLevel',
  label,
  isRequired = true,
  cookModels,
  nodeIsSelectable,
  isDisabled,
  gqlVariables = {},
  columns,
  excludeId,
  canRenderAdapter,
  enableLibraryFilter = false,
  ...props
}) => {
  const t = useT();
  const { me } = useCurrentUser();

  const status = useMemo(() => [Active.value, Draft.value], []);

  // State for library filter
  const [filterValues, setFilterValues] = useState<ILibraryFilterValue>({
    library: null,
    searchQuery: '',
  });

  // Fetch libraries when library filter is enabled
  const { data: librariesData } = useQuery(librariesQuery, {
    variables: {
      organisationGroupId: me?.organisationGroupId,
      status,
      count: 1000,
      first: 0,
      searchQuery: '',
    },
    skip: !enableLibraryFilter || !me?.organisationGroupId,
    fetchPolicy: 'cache-first',
  });

  const libraries = useMemo(
    () => get(librariesData, 'eContentLibraries', []) as IEContentLibrary[],
    [librariesData],
  );

  // Set default library if libraryId is provided and library filter is enabled
  useEffect(() => {
    if (
      enableLibraryFilter &&
      libraryId &&
      libraries.length > 0 &&
      !filterValues.library
    ) {
      const defaultLibrary = libraries.find(lib => lib.id === libraryId);
      if (defaultLibrary) {
        setFilterValues((prev: ILibraryFilterValue) => ({
          ...prev,
          library: defaultLibrary,
        }));
      }
    }
  }, [enableLibraryFilter, libraryId, libraries, filterValues.library]);

  // Determine which library ID to use
  const effectiveLibraryId = useMemo(() => {
    if (enableLibraryFilter) {
      return filterValues.library?.id;
    }
    return libraryId;
  }, [enableLibraryFilter, filterValues.library, libraryId]);

  const predicatesMeta = useMemo(
    () => [
      {
        valuePropName: 'status',
        predicate: () => (node: TEContentLibraryResourcesTreeTypes) =>
          includes(status, node?.status),
        includeChildren: false,
        collectVisibleIds: true,
      },
      {
        valuePropName: 'searchQuery',
        predicate: (value: string) => (
          node: TEContentLibraryResourcesTreeTypes,
        ) => catsearch(node.name, value),
        includeChildren: false,
        collectVisibleIds: true,
      },
    ],
    [status],
  );

  const filterComponent = useCallback(() => {
    const handleSearchChange = (searchQuery: string) => {
      setFilterValues(prev => ({ ...prev, searchQuery }));
    };

    return (
      <>
        <FilterBar
          searchName="searchQuery"
          values={filterValues}
          onChange={setFilterValues}
          {...props}
        >
          <FilterBar.LibrarySelector
            isSingleSelect
            models={libraries}
            name="library"
          />
        </FilterBar>
        <SearchField
          placeholder={t('Search')}
          value={filterValues.searchQuery}
          onChange={handleSearchChange}
        />
      </>
    );
  }, [props, libraries, filterValues, t]);

  const renderNodeAddons = (node: TEContentLibraryResourcesTreeTypes) => {
    const { id } = node;

    const addons: JSX.Element[] = [];
    const actions: IActionsAddonAction[] = [];

    if (!isRootNode(node)) {
      addons.push(
        <StatusAddon
          key={`status_${id}`}
          customActiveStatus={Active}
          customDeletedStatus={Deleted}
          customStatuses={StatusWithDraft.BasicByValue}
          status={node.status}
        />,
      );
    }

    if (!isEmpty(actions)) {
      addons.push(
        <ActionsAddon
          key={`actions_${node.id}`}
          actions={actions}
          node={node}
        />,
      );
    }

    return addons;
  };

  const nodeIsVisible = useCallback(
    node => (excludeId ? node.id !== excludeId : true),
    [excludeId],
  );

  return (
    <TreeSelectorField<TEContentLibraryResourcesTreeTypes>
      hasSyntheticRootNode
      adapter={{
        renderNodeAddons,
        resolveNodeId,
        resolveNodeParentId,
        resolveNodeClassName,
        ...(canRenderAdapter && { canRender }),
      }}
      columns={columns}
      cookModels={cookModels || defaultSelectionFilter}
      disabled={isDisabled}
      filter={
        enableLibraryFilter
          ? {
              component: filterComponent,
              predicatesMeta,
              value: filterValues,
              onChange: setFilterValues,
            }
          : { predicatesMeta }
      }
      gql={query}
      gqlFetchPolicy="cache-and-network"
      gqlSkip={!effectiveLibraryId}
      gqlVariables={{
        status,
        searchQuery: filterValues.searchQuery,
        ...gqlVariables,
        libraryId: effectiveLibraryId,
      }}
      hasSpinner={false}
      label={label || t('Parent Level')}
      name={name}
      plugins={{
        nodeIsVisible,
        nodeSortingOptions: nodeSortingOptionsSequence,
        nodeIsSelectable: nodeIsSelectable || defaultNodeIsSelectable,
      }}
      required={isRequired}
      selectionMode={SelectionMode.SINGLE}
      statuses={StatusWithDraft}
      syntheticRootNodeName={
        syntheticRootNodeName ||
        (enableLibraryFilter
          ? filterValues.library?.name || t('Select Library')
          : t('Library'))
      }
      tooltipPosition="responsive"
      {...props}
    />
  );
};

export default EContentLibraryResourcesTreeSelectorField;

const defaultSelectionFilter = (items: TEContentLibraryResourcesTreeTypes[]) =>
  filter(items, item => isEContentLibraryFolder(item) || isRootNode(item));

const defaultNodeIsSelectable = (item: TEContentLibraryResourcesTreeTypes) =>
  isEContentLibraryFolder(item) || isRootNode(item);
