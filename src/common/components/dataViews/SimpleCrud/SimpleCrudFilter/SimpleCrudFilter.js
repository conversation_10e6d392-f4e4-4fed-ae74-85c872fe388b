import { map, get } from 'lodash';
import PropTypes from 'prop-types';
import React from 'react';
import withPaginationRouting from '../../../../data/withPaginationRouting';

import SearchField from '../../../controls/base/SearchField';
import FilterBar from '../../../controls/FilterBar';
import { PageSizeSelectForCards } from '../../../other/PageSizeSelect';
import withTranslations from '../../../utils/Translations/withTranslations';
import { filter } from '../propTypes';
import countSelectorTypes, { isCardsList } from '../countSelectorType';
import PageSizeSelect from '../../../other/PageSizeSelect/PageSizeSelect';
import styles from './SimpleCrudFilter.scss';

@withTranslations
@withPaginationRouting
class SimpleCrudFilter extends React.PureComponent {
  static propTypes = {
    onChangeFilter: PropTypes.func.isRequired,
    countSelectorType: PropTypes.oneOf(countSelectorTypes),
    filterComponent: PropTypes.objectOf(PropTypes.func),
    filterComponentProps: PropTypes.object,
    filterKey: PropTypes.string,
    filterGroups: PropTypes.arrayOf(
      PropTypes.shape({
        names: PropTypes.arrayOf(PropTypes.string).isRequired,
      }),
    ),
    hasCollapsableFilters: PropTypes.bool,
    hasGridPagination: PropTypes.bool,
    isAllPageOptions: PropTypes.bool,
    hasPagination: PropTypes.bool,
    hasPaginationRouting: PropTypes.bool,
    onBeforeFilterUpdate: PropTypes.func,
    onBeforeFilterComplete: PropTypes.func,
    searchProps: PropTypes.object,
    ...filter.propTypes,
    ...withTranslations.props,
  };

  static defaultProps = {
    filterGroups: [],
    isFilterBarVisible: true,
    filterComponentProps: {},
    hasCollapsableFilters: false,
    hasGridPagination: false,
    isAllPageOptions: false,
    hasPagination: true,
    countSelectorType: 'cards-list',
    hasPaginationRouting: false,
    ...filter.defaultProps,
  };
  state = {
    queryFilter: false,
  };

  componentDidUpdate(prevProps) {
    const {
      pageSize,
      pageNumber,
      filter,
      hasPaginationRouting,
      hasGridPagination,
      onChangeFilter,
    } = this.props;

    if (
      hasPaginationRouting &&
      (prevProps.pageSize !== pageSize || prevProps.pageNumber !== pageNumber)
    ) {
      onChangeFilter({
        ...filter,
        first: (pageNumber - 1) * pageSize,
        count: pageSize,
      });
    }
  }

  get filterComponent() {
    const { filterComponent, filterComponentProps } = this.props;

    if (filterComponent) {
      return map(filterComponent, (Component, name) => (
        <Component
          key={name}
          filter={filter}
          name={name}
          {...get(filterComponentProps, name, {})}
        />
      ));
    }

    return <FilterBar.StatusSelector name="status" />;
  }

  handlePageSizeChange = count => {
    const { onChangeFilter, filter } = this.props;
    onChangeFilter({ ...filter, count, first: 0 });
  };

  handleFilterChange = filter => {
    const {
      onChangeFilter,
      filter: oldFilter,
      filterKey,
      pageSize,
      pageNumber,
      hasPaginationRouting,
    } = this.props;
    const values = {
      ...oldFilter,
      ...filter,
    };

    if (filterKey) {
      values.searchQuery = oldFilter.searchQuery;
    }

    if (hasPaginationRouting && !isFinite(filter.first)) {
      values.first = (pageNumber - 1) * pageSize;
    }

    if (hasPaginationRouting && !filter.count) {
      values.count = pageSize;
    }
    if (this.state.queryFilter) {
      this.setState({ queryFilter: false });
    } else {
      onChangeFilter(values);
    }
  };

  handleSearchQueryChange = searchQuery => {
    const {
      onChangeFilter,
      filter,
      setPageNumber,
      hasPaginationRouting,
    } = this.props;
    this.setState({ queryFilter: true });
    if (hasPaginationRouting && filter.searchQuery !== searchQuery) {
      setPageNumber(1);
    }
    onChangeFilter({ ...filter, searchQuery });
  };

  render() {
    const {
      filter,
      filterGroups,
      filter: { searchQuery, count },
      filterKey,
      hasCollapsableFilters,
      countSelectorType,
      hasPagination,
      hasPaginationRouting,
      onBeforeFilterUpdate,
      onBeforeFilterComplete,
      searchProps,
      hasGridPagination,
      isAllPageOptions,
      onFilterChanged,
      hasSearchFieldOnly,
    } = this.props;

    const CountSelector =
      isCardsList(countSelectorType) || !!hasGridPagination ? (
        <PageSizeSelectForCards
          isAllPageOptions={isAllPageOptions}
          value={count}
          onChange={this.handlePageSizeChange}
        />
      ) : (
        <PageSizeSelect
          hasPaginationRouting={hasPaginationRouting}
          value={count}
          onChange={this.handlePageSizeChange}
        />
      );

    return (
      <>
        {!hasSearchFieldOnly && (
          <FilterBar
            filterGroups={filterGroups}
            filterKey={filterKey}
            hasPaginationRouting={hasPaginationRouting}
            isHidingEnabled={hasCollapsableFilters}
            searchName="searchQuery"
            values={filter}
            onBeforeComplete={onBeforeFilterComplete}
            onBeforeUpdate={onBeforeFilterUpdate}
            onChange={this.handleFilterChange}
            onFilterChanged={onFilterChanged}
          >
            {this.filterComponent}
          </FilterBar>
        )}

        <div className="row datatable-header no-border-bottom">
          <SearchField
            isInTable
            inputClassName={styles.pr40}
            searchProps={searchProps}
            value={searchQuery}
            onChange={this.handleSearchQueryChange}
          />
          {hasPagination ? (
            <div className="col-sm-1 col-sm-offset-5 col-md-offset-7">
              {CountSelector}
            </div>
          ) : null}
        </div>
      </>
    );
  }
}

export default SimpleCrudFilter;
