/* global $ */
import classnames from 'classnames';
import {
  filter,
  find,
  get,
  isEqual,
  isUndefined,
  map,
  startsWith,
} from 'lodash';
import PropTypes from 'prop-types';
import React from 'react';
import { Redirect, Route, Switch, withRouter } from 'react-router-dom';
import uuidv4 from 'uuid/v4';
import Breadcrumbs from '../../../../common/components/utils/Breadcrumbs';

import EplusPropTypes from '../../../propTypes';
import mergeRoute from '../../../utils/mergeRoute';
import ArrowButton from '../../controls/ArrowButton';
import IconButton from '../../controls/IconButton';
import Scroll from '../../other/Scroll';
import withAddModeContainer from '../AddMode/withAddModeContainer';
import Popover from '../Popover';

import Tab from './Tab';
import TabGroup from './TabGroup';
import TabHeader from './TabHeader';

import styles from './Tabs.scss';
import { TabsSaverContext } from './TabsSaver';

const rearrangeMenuMode = {
  update: 0,
  setup: 1,
  resize: 2,
};

const WIDTH_GLITCH = 5;
const MIN_WIDTH_TO_ENABLE = 768;

@withAddModeContainer
@withRouter
export default class Tabs extends React.PureComponent {
  static propTypes = {
    children: EplusPropTypes.elementsOfType(Tab),
    horizontal: PropTypes.bool,
    hasPagination: PropTypes.bool,
    renderPaginationFooter: PropTypes.func,
    subLevel: PropTypes.bool,
    className: PropTypes.string,
    defaultDisabled: PropTypes.bool,
    isRounded: PropTypes.bool,
    isJustified: PropTypes.bool,
    hasRoutes: PropTypes.bool,
    tabStyle: PropTypes.string,
    additionalClasses: PropTypes.array,
    contentClassName: PropTypes.string,
    allowCollapse: PropTypes.bool,
    saveTabs: PropTypes.bool,
    hasResponsiveMenu: PropTypes.bool,
    isHidden: PropTypes.bool,
    onChange: PropTypes.func,
    currentTabProp: PropTypes.string,
    ...EplusPropTypes.routePropTypes,
  };

  static defaultProps = {
    children: [],
    horizontal: false,
    subLevel: false,
    className: '',
    contentClassName: '',
    defaultDisabled: false,
    tabStyle: 'default',
    additionalClasses: [],
    isRounded: false,
    isJustified: false,
    hasRoutes: true,
    allowCollapse: true,
    saveTabs: true,
    hasResponsiveMenu: false,
    isHidden: false,
    currentTabProp: null,
  };

  state = {
    currentTab: this.props.currentTabProp || null,
    isNavCollapsed: false,
    isMenuVisible: false,
    breakWidths: [],
  };

  componentDidMount() {
    const {
      match: { url },
      goToPreview,
    } = this.props;
    const { hasResponsiveMenu } = this.props;
    if (hasResponsiveMenu) {
      window &&
        window.addEventListener(
          'resize',
          this.rearrangeMenu.bind(this, rearrangeMenuMode.resize),
        );
      this.rearrangeMenuDelayed(rearrangeMenuMode.setup);
    }
    if (goToPreview) this.handleClick(`${url}/preview`);
  }

  componentDidUpdate(prevProps, prevState, snapshot) {
    const {
      children,
      match: { url },
      location: { pathname },
      saveTabs,
      hasResponsiveMenu,
      onChange,
      currentTabProp,
    } = this.props;

    // Update currentTab when currentTabProp changes
    if (currentTabProp !== prevProps.currentTabProp) {
      // eslint-disable-next-line react/no-did-update-set-state
      this.setState({ currentTab: currentTabProp });
    }

    if (!isEqual(prevProps.children, children) && hasResponsiveMenu) {
      this.rearrangeMenu();
    }

    const routes = filter(map(children, children => children?.props?.route));

    const lastTab = find(routes, route =>
      startsWith(pathname, mergeRoute(url, route)),
    );

    if (
      saveTabs &&
      this.context?.setLastTab &&
      this.context?.lastTab !== lastTab
    ) {
      this.context.setLastTab(lastTab);
    }

    // Call onChange when route changes (for route-based tabs)
    if (onChange && prevProps.location.pathname !== pathname) {
      const selectedTab = this.getSelectedTabInfoByRoute(pathname);
      if (selectedTab) {
        onChange(selectedTab);
      }
    }
  }

  componentWillUnmount() {
    const { hasResponsiveMenu } = this.props;
    if (hasResponsiveMenu) {
      window &&
        window.removeEventListener(
          'resize',
          this.rearrangeMenu.bind(rearrangeMenuMode.resize),
        );
    }
  }

  static contextType = TabsSaverContext;
  static Tab = Tab;
  static Group = TabGroup;

  refNav = event => (this.nav = event);
  refDropdownNav = event => (this.dropdownNav = event);

  rearrangeMenuDelayed = mode => setTimeout(() => this.rearrangeMenu(mode), 0);

  rearrangeMenu = (mode = rearrangeMenuMode.update) => {
    if (!this.nav) {
      return;
    }

    const $nav = $(this.nav);
    const $menuButton = $('.menuButton');
    $menuButton.removeClass('hidden');

    const availableSpace = $nav.outerWidth();
    let requiredSpace = 0;

    if (window && window.innerWidth < MIN_WIDTH_TO_ENABLE) {
      $menuButton.addClass('hidden');
      $nav.children('li').removeClass('hidden');
      return;
    }

    if (
      mode === rearrangeMenuMode.setup ||
      $nav.children('li').length !== this.state.breakWidths.length
    ) {
      let requiredSpace = 0;
      const breakWidths = [];

      map($nav.children('li'), child => {
        const $element = $(child).clone().appendTo($nav).removeClass('hidden');
        const width = $element.outerWidth();
        $element.remove();

        requiredSpace += width;
        breakWidths.push(requiredSpace);
        this.setState({ breakWidths });
      });
    }

    $nav.children('li:not(.hidden)').outerWidth((i, width) => {
      requiredSpace += width;
    });

    const { breakWidths } = this.state;

    let hiddenTabs = $nav.children('li.hidden:not(.menuButton)');
    const firstHiddenTab = hiddenTabs.first();
    const firstHiddenTabIndex = $nav.children('li').index(firstHiddenTab);

    let alwaysVisibleTabsWidth = 0;

    $nav.children('li.always-visible').outerWidth((i, width) => {
      alwaysVisibleTabsWidth += width;
    });

    if (requiredSpace + WIDTH_GLITCH > availableSpace) {
      const visibleTabs = $nav.children('li:not(.always-visible):not(.hidden)');

      visibleTabs.last().addClass('hidden');
    } else if (
      availableSpace >
      breakWidths[firstHiddenTabIndex] + alwaysVisibleTabsWidth + WIDTH_GLITCH
    ) {
      firstHiddenTab.removeClass('hidden');
    }

    hiddenTabs = $nav.children('li.hidden:not(.menuButton)');

    if (hiddenTabs.length) {
      $menuButton.removeClass('hidden');
    } else {
      $menuButton.addClass('hidden');
    }

    if ($nav.children('li.active:not(.menuButton):not(.hidden)').length) {
      $menuButton.removeClass('active');
    } else {
      $menuButton.addClass('active');
    }
  };

  get routes() {
    const { children, match, defaultDisabled } = this.props;

    return React.Children.map(children, child => {
      if (!child) return null;

      const {
        hidden,
        title,
        tooltip,
        customActive,
        isBolder,
        rightLabel,
        route,
        children,
        divider,
        exact,
        disabled,
        default: isDefault,
        className,
        isAlwaysVisible,
        action,
        isNavigationAllowed,
      } = child.props;

      if (hidden) {
        return null;
      }

      return {
        title,
        tooltip,
        customActive,
        isBolder,
        rightLabel,
        disabled: isUndefined(disabled) ? defaultDisabled : disabled,
        url: mergeRoute(match.url, route),
        path: mergeRoute(match.path, route),
        exact: exact || (isDefault && !route),
        children,
        divider,
        className,
        isAlwaysVisible,
        isNavigationAllowed,
        action,
      };
    }).filter(child => child);
  }

  get defaultRoute() {
    const {
      children,
      match,
      location: { hash, search, state },
      saveTabs,
    } = this.props;

    let defaultRoute = '';

    if (saveTabs && this.context?.lastTab) {
      defaultRoute = this.context.lastTab;
    } else if (Array.isArray(children)) {
      defaultRoute = get(
        find(children, child => child?.props?.default) || children[0],
        'props.route',
      );
    } else if (children) {
      defaultRoute = get(children, 'props.route');
    } else {
      return { pathname: match.url };
    }

    return {
      hash,
      search,
      state,
      pathname: mergeRoute(match.url, defaultRoute),
    };
  }

  handleCollapseNavigation = event => {
    event && event.preventDefault();
    this.setState(state => ({ isNavCollapsed: !state.isNavCollapsed }));
  };

  rebuildDropdownMenu = () => {
    const { hasResponsiveMenu } = this.props;
    if (!hasResponsiveMenu) {
      return;
    }

    const $nav = $(this.nav);
    const hiddenTabs = $nav.children('li.hidden:not(.menuButton)');

    if (hiddenTabs.length && this.dropdownNav) {
      const dropdownItems = $(this.dropdownNav).children('li');
      dropdownItems.addClass('hidden');
      hiddenTabs.each((i, child) => {
        const index = $nav.children('li').index(child);
        dropdownItems.eq(index).removeClass('hidden');
      });
    }
  };

  handleToggleMenu = e => {
    e && e.preventDefault();
    this.setState(
      state => ({ isMenuVisible: !state.isMenuVisible }),
      this.rebuildDropdownMenu,
    );
  };

  handleCloseMenu = e => {
    e && e.preventDefault();
    this.setState({ isMenuVisible: false });
  };

  handleClick = url => {
    this.setState({ currentTab: url, isMenuVisible: false });

    // Call onChange callback with selected tab information
    const { onChange } = this.props;
    if (onChange) {
      const selectedTab = this.getSelectedTabInfo(url);
      onChange(selectedTab);
    }
  };

  getSelectedTabInfo = url => {
    const { children, match } = this.props;

    // Find the tab that matches the URL
    let tabInfo = null;

    React.Children.forEach(children, (child, index) => {
      if (!child) return;

      const { route, title } = child.props;
      const tabUrl = mergeRoute(match.url, route);

      if (tabUrl === url) {
        tabInfo = {
          id: route || index,
          index,
          route: route || '',
          title: title || '',
          url: tabUrl,
        };
      }
    });

    return tabInfo;
  };

  getSelectedTabInfoByRoute = pathname => {
    const { children, match } = this.props;

    // Find the tab that matches the current pathname
    let tabInfo = null;

    React.Children.forEach(children, (child, index) => {
      if (!child) return;

      const { route, title } = child.props;
      const tabUrl = mergeRoute(match.url, route);

      if (startsWith(pathname, tabUrl)) {
        tabInfo = {
          id: route || index,
          index,
          route: route || '',
          title: title || '',
          url: tabUrl,
        };
      }
    });

    return tabInfo;
  };

  get menuButton() {
    const { isMenuVisible } = this.state;
    const anchorId = uuidv4();

    return (
      <li className="menuButton always-visible">
        <IconButton
          className="cursor-pointer"
          hasBackground={false}
          iconName="menu"
          onClick={this.handleToggleMenu}
        />
        <div className={styles.menuAnchor} id={anchorId} />
        <Popover
          hasLockedPosition
          anchorId={anchorId}
          contentWrapperClassName={styles.menuDropdown}
          position="bottom"
          visible={isMenuVisible}
          onClose={this.handleCloseMenu}
        >
          <Scroll autoHeightMax={180}>{this.renderTabsList(true)}</Scroll>
        </Popover>
      </li>
    );
  }

  renderTabHeaders = routes => {
    const { hasRoutes } = this.props;
    const { currentTab } = this.state;
    return routes.map(
      (
        {
          title,
          tooltip,
          customActive,
          isBolder,
          rightLabel,
          disabled,
          path,
          url,
          exact,
          divider,
          className,
          isAlwaysVisible,
          action,
          isNavigationAllowed,
        },
        index,
      ) => (
        <TabHeader
          key={url}
          action={action}
          className={classnames(className, {
            'always-visible': isAlwaysVisible,
          })}
          currentTab={currentTab}
          customActive={customActive}
          disabled={disabled}
          divider={divider}
          exact={exact}
          hasRoutes={hasRoutes}
          index={index + ~~isAlwaysVisible}
          isBolder={isBolder}
          isNavigationAllowed={isNavigationAllowed}
          path={path}
          rightLabel={rightLabel}
          title={title}
          tooltip={tooltip}
          url={url}
          onClick={this.handleClick}
        />
      ),
    );
  };

  renderTabsList = (isDropDownMenu = false) => {
    const {
      subLevel,
      tabStyle,
      additionalClasses,
      isRounded,
      isJustified,
      allowCollapse,
      horizontal,
      hasResponsiveMenu,
      hasPagination,
      renderPaginationFooter,
      isHidden,
    } = this.props;

    const ulClassname =
      String(tabStyle) === 'white' || String(tabStyle) === 'no-margin-bottom'
        ? 'no-margin-bottom'
        : '';
    const { isNavCollapsed } = this.state;

    const routes = filter(this.routes, ({ isAlwaysVisible }) =>
      hasResponsiveMenu ? !isAlwaysVisible : true,
    );

    const alwaysVisibleTabs = filter(this.routes, { isAlwaysVisible: true });

    return (
      <ul
        ref={isDropDownMenu ? this.refDropdownNav : this.refNav}
        className={classnames(
          'nav',
          ulClassname,
          styles.nocontent,
          additionalClasses.map(className => styles[className]),
          {
            [styles.tabsWidth]: !isNavCollapsed && !horizontal,
            [styles.tabsWidthCollapsed]: isNavCollapsed,
            [styles.navVertical]: !horizontal,
            'nav-tabs': !isNavCollapsed,
            'nav-tabs-highlight': !isRounded,
            'bg-slate nav-tabs-component': isRounded,
            'nav-justified': isJustified,
            'nav-tabs-bottom': subLevel,
            hidden: isHidden,
          },
        )}
      >
        {allowCollapse && !horizontal && (
          <li>
            <ArrowButton
              className={classnames('hidden-xs', {
                [styles['toggle-btn']]: !isNavCollapsed,
              })}
              hasExtraContentVisible={isNavCollapsed}
              icon="arrow-right2"
              onArrowClick={this.handleCollapseNavigation}
            />
          </li>
        )}
        {this.renderTabHeaders(routes)}
        {hasResponsiveMenu && horizontal && !isDropDownMenu ? (
          <>
            {this.menuButton}
            {this.renderTabHeaders(alwaysVisibleTabs)}
          </>
        ) : null}
        {hasPagination && (
          <li>
            <div className={classnames('col-md-12')}>
              {renderPaginationFooter}
            </div>
          </li>
        )}
      </ul>
    );
  };

  render() {
    const {
      horizontal,
      className,
      tabStyle,
      hasRoutes,
      contentClassName,
      isHidden,
    } = this.props;

    const { currentTab } = this.state;

    return (
      <div
        className={classnames('tabbable', className, {
          'nav-tabs-vertical nav-tabs-left': !horizontal,
        })}
      >
        {this.renderTabsList()}
        <div
          className={classnames(
            'panel-body',
            horizontal ? styles.tabHorizontal : styles.tabContent,
            contentClassName,
            styles[tabStyle],
            {
              'no-padding-left': isHidden,
            },
          )}
        >
          <div className="tab-pane active">
            {!hasRoutes ? (
              this.routes
                .filter(route => !route.disabled)
                .map(({ url, children }) =>
                  currentTab
                    ? currentTab === url && children
                    : this.defaultRoute.pathname === url && children,
                )
            ) : (
              <Switch>
                {this.routes
                  .filter(route => !route.disabled)
                  .map(({ title, path, url, exact, children }) => (
                    <Route key={url} exact={exact} path={path}>
                      <Breadcrumbs.Anchor route={url} title={title}>
                        {children}
                      </Breadcrumbs.Anchor>
                    </Route>
                  ))}
                {this.defaultRoute && <Redirect to={this.defaultRoute} />}
              </Switch>
            )}
          </div>
        </div>
      </div>
    );
  }
}
