import classNames from 'classnames';
import React, { FC } from 'react';
import styles from './UnreadMessagesCount.scss';
import useUnreadMessagesCount from '../../../data/hooks/useUnreadMessagesCount';

const UnreadMessagesCountHook: FC<IUnreadMessagesCountHookProps> = ({
  messageId,
}) => {
  const { unreadMessagesCount } = useUnreadMessagesCount(messageId);
  console.log('unreadMessagesCount', messageId, unreadMessagesCount);
  const unreadCount = unreadMessagesCount || 0;
  return unreadCount ? (
    <span className={classNames(styles.count, 'count')}>{unreadCount}</span>
  ) : null;
};

export default UnreadMessagesCountHook;

interface IUnreadMessagesCountHookProps {
  messageId: number;
}
