import { IClassStaffMarkbookPreference } from '../MySpace/MyLearningSpaceStaff/Markbook/IClassStaffMarkbookPreference';
import IEContentResource from '../EContent/IEContentResource';
import { TStatus } from '../../propTypes';
import { TStatusWithDraft } from '../../../model/StatusWithDraft';

export interface IPersonPreferences {
  id?: string;
  UI_LANGUAGE?: string | null;
  TIME_ZONE?: string | undefined;
  MAIN_ORGANISATION?: string | null;
  TIMETABLE_STAFF_COLUMNS?: string[] | null;
  TIMETABLE_STUDENT_COLUMNS?: string[] | null;
  NOTIFICATION_ED_COM_ORGANISATION?: {
    organisationId: number;
    orgStructureLevelId: number;
    personEntityAllocationId: number;
    emailAddress?: string;
  } | null;
  ED_COM_MESSAGES_ORGANISATION?: {
    organisationId: number;
    orgStructureLevelId: number;
    personEntityAllocationId: number;
  } | null;
  EDIT_SESSION?: boolean;
  QUICK_LINK_SETTINGS?: string;
  CLASS_STAFF_PREFERENCES?: IClassStaffMarkbookPreference;
  ORGANISATION_GROUP_ID_FILTER?: number;
  WEEK_STARTS_FROM?: number;
  LAST_PATHNAME?: string;
  LAST_PATHNAME_MODULES?: ILastPathnameModules[];
  E_CONTENT_SELECTED_RESOURCE?: Partial<IEContentResource>;
  E_CONTENT_QUESTIONS_COUNT?: number;
  E_CONTENT_GPT_VERSION?: string;
  E_CONTENT_TEXT_TRANSFORM_GPT_VERSION?: string;
  LMS_PROGRAM_INTAKE_FILTERS?: {
    INTAKE_STATUS?: string[];
    ORGANISATIONS?: OrganisationFilter[];
    PROGRAM_GROUP?: number[];
    PROGRAMS?: ProgramXOrgStructLevelFilter[];
  };
  E_CONTENT_RESOURCE_LIBRARY_FILTER?: {
    LIBRARY_ID?: number[];
    STATUS?: TStatusWithDraft[];
  };
  LMS_STAFF_LEARNING_SPACE_FILTERS?: {
    INTAKE_STATUS?: string[];
    ORGANISATION_GROUP?: OrganisationGroupFilter;
    ORGANISATIONS?: OrganisationFilter[];
    PROGRAM_GROUP?: number[];
    PROGRAMS?: ProgramXOrgStructLevelFilter[];
  };
  LMS_STUDENT_LEARNING_SPACE_FILTERS?: {
    INTAKE_STATUS?: string[];
    ORGANISATION_GROUP?: OrganisationGroupFilter;
    ORGANISATIONS?: OrganisationFilter[];
    PROGRAM_GROUP?: number[];
    PROGRAMS?: ProgramXOrgStructLevelFilter[];
  };
  LMS_TIMETABLE_FILTERS?: {
    STATUS?: string[];
    ORGANISATIONS?: OrganisationFilter[];
  };
  E_CONTENT_TRANSLATION_SUPPORT_SUMMARY?: {
    LANGUAGE_TO?: number;
  };
  E_CONTENT_TRANSLATION_SUPPORT_COMPARE?: {
    LANGUAGE_FROM?: number;
    LANGUAGE_TO?: number;
  };
  E_CONTENT_TRANSLATION_SUPPORT_SEARCH?: {
    LANGUAGE_ID?: number[];
  };
  TEXT_TO_AUDIO?: {
    AI_MODEL?: string;
    LANGUAGE?: string;
    GENDER?: string;
    SPEAKER?: string;
    SPEED?: string;
    PITCH?: string;
    VOLUME_GAIN?: string;
    FREQUENCY?: string;
    AGE_GROUP?: string;
    STYLE?: string;
  };
}

export interface ILastPathnameModules {
  module: string;
  route: string;
}

export interface OrganisationGroupFilter {
  id: number;
  name: string;
  status: TStatus;
}

export interface OrganisationFilter {
  id: number;
  name: string;
  orgGroupId: number;
  organisationId: number;
  organisationStructureId: number;
  parentId: number;
  sequence: number;
  status: TStatus;
  type: string;
  organisationType: string;
}

export interface ProgramXOrgStructLevelFilter {
  id: number;
  intakeId: number;
  name: string;
  organisationStructureLevelId: number;
  programGroupId: number;
  programId: number;
  status: TStatus;
  treeSequence: TreeSequenceFilter;
  programGroupTypeId: number;
  __typename?: string;
}

interface TreeSequenceFilter {
  parentSequence: number;
  sequence: number;
}
