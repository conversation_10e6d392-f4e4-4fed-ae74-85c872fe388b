import classNames from 'classnames';
import html2canvas from 'html2canvas-pro';
import { jsPDF } from 'jspdf';
import { find, isEmpty, isString, map, orderBy } from 'lodash';
import React, {
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';

import IEContentQuestion from '../../../../../../../../../common/abstract/EContent/IEContentQuestion';
import EntityForm from '../../../../../../../../../common/components/containers/EntityForm';
import { IBasicEntity } from '../../../../../../../../../common/components/containers/EntityForm/EntityForm';
import useEntityFormContext from '../../../../../../../../../common/components/containers/EntityForm/internal/useEntityFormContext';
import SelectBox, {
  RETURN_TYPE,
} from '../../../../../../../../../common/components/controls/base/SelectBox';
import useEditorImageProcess from '../../../../../../../../../common/components/controls/base/TextEditor/useEditorImageProcess';
import useStorageSettings from '../../../../../../../../../common/components/controls/base/TextEditor/useStorageSettings';
import IconButton from '../../../../../../../../../common/components/controls/IconButton';
import PanelButtons, {
  AddButtonMultiple,
  PanelButton,
} from '../../../../../../../../../common/components/controls/PanelButtons';
import RoundedGoBackButton from '../../../../../../../../../common/components/controls/RoundedGoBackButton';
import StaticTable from '../../../../../../../../../common/components/dataViews/Tables/StaticTable';
import { getCellSortBy } from '../../../../../../../../../common/components/dataViews/Tables/Table/TableContent/TableBody/TableRowCellContent';
import Modal from '../../../../../../../../../common/components/utils/Modal';
import Strike from '../../../../../../../../../common/components/utils/Strike';
import useT from '../../../../../../../../../common/components/utils/Translations/useT';
import useCurrentUser from '../../../../../../../../../common/data/hooks/useCurrentUser';
import useFsClient from '../../../../../../../../../common/data/hooks/useFsClient';
import {
  ISimpleTableConfig,
  TSortOrder,
} from '../../../../../../../../../common/propTypes';
import Notifications from '../../../../../../../../../common/utils/Notifications';
import { E_CONTENT_CONTENT_FILE } from '../../../../../../../../../fsCategories';
import QuestionType, {
  BasicById,
  basicQuestion,
  ESSAY_QUESTION,
  NUMBER_QUESTION,
  PARAGRAPH_QUESTION,
  SHORT_TEXT,
} from '../../../../../../../../../model/QuestionType';
import useSubmitDefault from '../../../../../../../../EdCom/Notifications/common/useSubmitDefault';
import QuestionFormElements from '../Question/QuestionFormElements';
import IQuestionQuick from './abstract/IQuestionQuick';
import ArrowButtonsTitle from './ArrowButtonsTitle';
import QuestionAutoGenerate from './QuestionAutoGenerate';
import QuestionQuickAdd from './QuestionQuickAdd';
import styles from './QuestionTable.scss';
import tableColumns from './tableColumns';
import tableColumnsPreviewQuestions from './tableColumnsPreviewQuestions';
import Spinner from '../../../../../../../../../common/components/utils/Spinner';
import EditSessionContext from '../../../../../../../../../common/components/containers/EditSessionProvider/EditSessionContext';

const MIN_LEVEL = 1;
const MAX_LEVEL = 20;
const DELAY = 500;
const SUBMIT_SIMPLE = 1;
const SUBMIT_NEXT = 2;
const SUBMIT_SELECT = 3;
const TEN = 10;

const QuestionTable: React.FC<{
  items: IEContentQuestion[];
  loading: boolean;
  maxNumberOfQuestion: number;
  contentId: number | undefined;
  onDelete: (index: number) => Promise<boolean>;
  onMove: (
    sequenceFrom: number,
    sequenceTo: number,
    itemsUpdated: IEContentQuestion[],
  ) => Promise<boolean>;
  onSubmit: (Item: any) => Promise<boolean>;
  onSubmitMultiple: (
    items: IEContentQuestion[],
    startSequence: number,
  ) => Promise<boolean>;
  isKeywordRequired?: boolean;
  maxTagLength?: number;
  hasKeyword?: boolean;
  suggestions?: string[];
  textList: string[];
  isAIQuestionsEnabled?: boolean;
  clobsPreviewData?: any;
  hasRenderedPreview?: boolean;
}> = ({
  items,
  loading,
  maxNumberOfQuestion,
  contentId,
  onDelete,
  onMove,
  onSubmit,
  onSubmitMultiple,
  maxTagLength,
  isKeywordRequired,
  hasKeyword,
  suggestions,
  textList,
  isAIQuestionsEnabled,
  clobsPreviewData,
  hasRenderedPreview,
}) => {
  const t = useT();
  const fsClient = useFsClient();
  const { me } = useCurrentUser();
  const { tenantId, organisationGroupId } = me;

  const { isEditSessionActive: isEditing } = useContext(EditSessionContext);
  const [currentNumber, setCurrentNumber] = useState<number>(0);
  const [activeQuestion, setActiveQuestion] = useState<number>(-1);
  const [startQuestion, setStartQuestion] = useState<number>(0);
  const [quickAddShow, setQuickAddShow] = useState<boolean>(false);
  const [quickAutoGenerate, setQuickAutoGenerate] = useState<boolean>(false);
  const [isReadOnly, setIsReadOnly] = useState(isEditing);
  const [nextQuestionType, setNextQuestionType] = useState(
    Number(localStorage.getItem('next_question_type')) || 0,
  );
  const [submitOption, setSubmitOption] = useState(
    Number(localStorage.getItem('next_question_option')) || SUBMIT_SIMPLE,
  );

  const submitOptionList = [
    { name: t('Simple'), value: SUBMIT_SIMPLE, sequence: 1 },
    { name: t('Save Next'), value: SUBMIT_NEXT, sequence: 2 },
    { name: t('Save Select'), value: SUBMIT_SELECT, sequence: 3 },
  ];

  const [selectedQuestion, setSelectedQuestion] = useState<IEContentQuestion>(
    {} as IEContentQuestion,
  );
  useEffect(() => {
    if (items) {
      setCurrentNumber(items ? items.length : 0);
    }
  }, [items]);
  const onActionAdd = useCallback(
    (id: number, index: number) => {
      const groupSequence = index >= 0 ? index + 1 : items.length;
      setStartQuestion(groupSequence);
      if (id === QuestionType.AUTO_GENERATE.id) {
        setQuickAutoGenerate(true);
      } else {
        const item: IEContentQuestion = {
          questionType: id,
          questionTypeId: 0,
          groupSequence,
          questionMultiple: false,
          questionMultipleId: 0,
          questionTitle: '',
          questionTitleId: 0,
          questionLevel: undefined,
          questionLevelId: 0,
          questionPoint: undefined,
          questionPointId: 0,
        };
        setIsReadOnly(false);
        setSelectedQuestion(item);
      }
    },
    [items],
  );
  const onActionEdit = useCallback(
    (index: number) => () => {
      setIsReadOnly(false);
      setSelectedQuestion(items[index]);
    },
    [items, setSelectedQuestion, setIsReadOnly],
  );
  const setNewRecord = useCallback(
    (index: number) => {
      items.length >= index && setSelectedQuestion(items[index]);
    },
    [items],
  );
  useEffect(() => {
    if (activeQuestion >= 0) {
      setIsReadOnly(false);
      setSelectedQuestion(items[activeQuestion]);
    }
  }, [activeQuestion, onActionEdit, setSelectedQuestion, setIsReadOnly]);
  const gotoQuestionDetails = useCallback(item => {
    setIsReadOnly(true);
    setSelectedQuestion(item);
  }, []);
  //Quick Question overlayy
  const showQuickAddModal = useCallback(() => setQuickAddShow(true), [
    setQuickAddShow,
  ]);
  const handleQuickAddCancel = useCallback(() => setQuickAddShow(false), [
    setQuickAddShow,
  ]);
  const handleQuickAddSubmit = useCallback(
    (item: IQuestionQuick) => {
      if (
        [
          NUMBER_QUESTION.id,
          SHORT_TEXT.id,
          PARAGRAPH_QUESTION.id,
          ESSAY_QUESTION.id,
        ].includes(selectedQuestion.questionType)
      ) {
        setSelectedQuestion({
          ...selectedQuestion,
          questionTitle: item.questionTitle,
          questionCorrectAnswer: item.questionOptions[0].description,
        });
      } else {
        setSelectedQuestion({
          ...selectedQuestion,
          questionTitle: item.questionTitle,
          options: item.questionOptions,
        });
      }

      handleQuickAddCancel();
      return true;
    },
    [handleQuickAddCancel, selectedQuestion],
  );

  const { maxSize } = useStorageSettings(E_CONTENT_CONTENT_FILE, tenantId);

  //Autogenerate overlay
  const handleAutoGenerateCancel = useCallback(
    () => setQuickAutoGenerate(false),
    [setQuickAutoGenerate],
  );
  const handleCancel = useCallback(() => {
    setActiveQuestion(-1);
    setSelectedQuestion({} as IEContentQuestion);
  }, [setSelectedQuestion]);
  const _onSubmit = async item => {
    item.questionTitle = await useEditorImageProcess({
      value: item.questionTitle,
      fileCategory: E_CONTENT_CONTENT_FILE,
      fsClient,
      tenantId,
      organisationGroupId,
      t,
      maxSize,
      isResizable: true,
    });
    if (!isEmpty(item.questionLevel)) {
      const level = parseFloat(item.questionLevel);
      if (!(level >= MIN_LEVEL && level <= MAX_LEVEL)) {
        Notifications.error(
          t(`Difficulty level should be between #{MIN_LEVEL} - #{MAX_LEVEL}`, {
            MIN_LEVEL,
            MAX_LEVEL,
          }),
          '',
          t,
        );
        return false;
      }
    }
    const res = await onSubmit(item);
    return res;
  };

  const _onSubmitOnly = useCallback(
    async item => {
      const res = await _onSubmit(item);
      if (!!res) {
        setTimeout(() => {
          setActiveQuestion(Number(item.groupSequence));
        }, DELAY);
      }
    },
    [setActiveQuestion, handleCancel, _onSubmit],
  );
  const _onSubmitNew = useCallback(
    async item => {
      const res = await _onSubmit(item);
      if (!!res) {
        const index =
          selectedQuestion && selectedQuestion.questionTypeId
            ? -1
            : currentNumber;
        onActionAdd(nextQuestionType, index);
      }
    },
    [onActionAdd, nextQuestionType, handleCancel, _onSubmit, selectedQuestion],
  );
  const _onSubmitNext = useCallback(
    async item => {
      const res = await _onSubmit(item);
      if (!!res) {
        const questionType = selectedQuestion.questionType || 0;
        const index =
          selectedQuestion && selectedQuestion.questionTypeId
            ? -1
            : currentNumber;
        onActionAdd(questionType, index);
      }
    },
    [onActionAdd, nextQuestionType, handleCancel, _onSubmit, selectedQuestion],
  );

  const _onSubmitClose = useCallback(
    async item => {
      const res = await _onSubmit(item);
      if (!!res) {
        setActiveQuestion(-1);
        handleCancel();
      }
    },
    [handleCancel, _onSubmit, setActiveQuestion],
  );
  const _onSubmitMain = useCallback(
    async item => {
      if (submitOption === SUBMIT_NEXT) {
        await _onSubmitNext(item);
      } else if (submitOption === SUBMIT_SELECT) {
        await _onSubmitNew(item);
      } else {
        await _onSubmitOnly(item);
      }
    },
    [submitOption, _onSubmitNew, _onSubmitOnly, _onSubmitNext],
  );
  const _onSubmitMultiple = useCallback(
    async (newItems: IEContentQuestion[], closeModal = true) => {
      if (maxNumberOfQuestion < newItems.length + items.length) {
        Notifications.error(
          'Error',
          `Attach at maximum ${maxNumberOfQuestion - items.length} question${
            maxNumberOfQuestion === 1 ? '' : 's'
          }`,
          t,
        );
        return false;
      }
      const res = await onSubmitMultiple(newItems, startQuestion);
      if (res && closeModal) handleAutoGenerateCancel();
      return res;
    },
    [
      handleAutoGenerateCancel,
      onSubmitMultiple,
      startQuestion,
      maxNumberOfQuestion,
      items,
      t,
    ],
  );
  const _onDelete = useCallback(
    async (index: number) => {
      const res = await onDelete(index);
      res && handleCancel();
      return true;
    },
    [handleCancel, onDelete],
  );
  const addButtonList = useCallback(
    () => (
      <>
        {contentId &&
          contentId > 0 &&
          isEditing &&
          currentNumber < maxNumberOfQuestion &&
          QuestionType &&
          map(QuestionType.Basic, element => {
            const onClickButton = function () {
              onActionAdd(element.id, -1);
            };
            return (
              <>
                {element.code === 'AG' && !isAIQuestionsEnabled ? (
                  <></>
                ) : (
                  <PanelButton
                    key={element.id}
                    badge={t(element.code)}
                    className={classNames(styles.roundButton)}
                    icon=""
                    title={t(`Add #{name} Question`, {
                      name: element.name,
                    })}
                    onClick={onClickButton}
                  />
                )}
              </>
            );
          })}
      </>
    ),
    [
      onActionAdd,
      contentId,
      currentNumber,
      maxNumberOfQuestion,
      isEditing,
      isAIQuestionsEnabled,
      t,
    ],
  );
  const [previewQuestionsShow, setPreviewQuestionsShow] = useState<boolean>(
    false,
  );
  const handlePreviewQuestionsShowCancel = useCallback(() => {
    setPreviewQuestionsShow(false);
  }, []);
  const openPreviewQuestionsModal = useCallback(() => {
    setPreviewQuestionsShow(true);
  }, []);

  const [
    previewQuestionsShowPdf,
    setPreviewQuestionsShowPdf,
  ] = useState<boolean>(false);
  const handlePreviewQuestionsShowPdfCancel = useCallback(() => {
    setPreviewQuestionsShowPdf(false);
  }, []);
  const openPreviewQuestionsPdfModal = useCallback(() => {
    setPreviewQuestionsShowPdf(true);
  }, []);

  const divRef = useRef(null);
  const tableRef = useRef(null);
  const [pdfUrl, setPdfUrl] = useState(''); // State to store the PDF URL
  const [pdfUrlLoading, setPdfUrlLoading] = useState(false); // State to store the PDF URL

  const generatePDF = () => {
    if (!tableRef.current || !divRef.current) return;
    setPdfUrlLoading(true);
    openPreviewQuestionsPdfModal();

    // eslint-disable-next-line @typescript-eslint/ban-ts-ignore
    // @ts-ignore
    const wasHidden = divRef.current.classList.contains('hidden');
    if (wasHidden) {
      // eslint-disable-next-line @typescript-eslint/ban-ts-ignore
      // @ts-ignore
      divRef.current.classList.remove('hidden');
    }

    // eslint-disable-next-line @typescript-eslint/ban-ts-ignore
    // @ts-ignore
    html2canvas(tableRef.current, {
      scale: 1,
      backgroundColor: '#ffffff', // Set a supported color format
      useCORS: true, // If images are used
    })
      .then(canvas => {
        if (wasHidden) {
          // eslint-disable-next-line @typescript-eslint/ban-ts-ignore
          // @ts-ignore
          divRef.current.classList.add('hidden');
        }

        const imgData = canvas.toDataURL('image/png'); // Convert to image
        const pdf = new jsPDF('p', 'mm', 'a4');

        const imgWidth = 210; // A4 width in mm
        const pageHeight = 297; // A4 height in mm
        const imgHeight = canvas.height;
        // const imgHeight = (canvas.height * imgWidth) / canvas.width; // Scale height proportionally

        let yPosition = 0; // Start from the top of the image
        let remainingHeight = imgHeight;

        while (remainingHeight > 0) {
          const canvasPage = document.createElement('canvas');
          canvasPage.width = canvas.width;
          canvasPage.height = Math.min(
            canvas.height - yPosition,
            (pageHeight * canvas.width) / imgWidth,
          );

          const ctx = canvasPage.getContext('2d');

          // eslint-disable-next-line @typescript-eslint/ban-ts-ignore
          // @ts-ignore
          ctx.drawImage(
            canvas,
            0,
            yPosition,
            canvas.width,
            canvasPage.height,
            0,
            0,
            canvas.width,
            canvasPage.height,
          );

          const pageImage = canvasPage.toDataURL('image/png'); // Get cropped page image
          pdf.addImage(
            pageImage,
            'PNG',
            0,
            TEN,
            imgWidth,
            (canvasPage.height * imgWidth) / canvas.width,
          );

          remainingHeight -= canvasPage.height;
          yPosition += canvasPage.height;

          if (remainingHeight > 0) pdf.addPage(); // Add new page for next slice
        }

        // Open PDF in a new tab
        // const pdfBlob = pdf.output('bloburl');
        const pdfBlob = pdf.output('blob');
        const pdfBlobUrl = URL.createObjectURL(
          new Blob([pdfBlob], { type: 'application/pdf' }),
        );
        setPdfUrl(pdfBlobUrl);
        setPdfUrlLoading(false);
        // window.open(pdfBlob, '_blank');
      })
      .catch(() => {
        setPdfUrlLoading(false);
      });
  };

  const previewButtonList = useCallback(
    () => (
      <>
        <PanelButton
          icon="file-eye"
          title={t(`Preview`)}
          onClick={openPreviewQuestionsModal}
        />
        <PanelButton
          icon="file-pdf"
          isDisabled={pdfUrlLoading}
          title={t(`PDF`)}
          onClick={generatePDF}
        />
      </>
    ),
    [t, openPreviewQuestionsModal, pdfUrlLoading],
  );
  const addNewButtonList = useCallback(
    () => (
      <>
        {!isReadOnly &&
          submitOption === SUBMIT_SELECT &&
          map(basicQuestion, element => {
            const onClickButton = function () {
              setNextQuestionType(Number(element.id));
              localStorage.setItem('next_question_type', element.id.toString());
            };

            return (
              <PanelButton
                key={element.id}
                badge={t(element.code)}
                className={classNames(
                  'mr-5 mb-5',
                  styles.roundButton,
                  nextQuestionType === element.id
                    ? styles.active
                    : styles.inactive,
                )}
                icon=""
                title={t(`#{name} Question`, {
                  name: element.name,
                })}
                onClick={onClickButton}
              />
            );
          })}
      </>
    ),
    [nextQuestionType, isReadOnly, submitOption, t],
  );
  const onChangeOption = useCallback(
    id => {
      setSubmitOption(id);
      localStorage.setItem('next_question_option', id.toString());
    },
    [setSubmitOption],
  );
  const addSubmitOption = useCallback(
    (isSubmitting: boolean) => (
      <SelectBox
        disabled={!!isSubmitting}
        itemTitlePropName="name"
        itemValuePropName="value"
        options={submitOptionList}
        placeholder={t('Save  Mode')}
        returnType={RETURN_TYPE.NUMBER}
        value={submitOption}
        withTitle={false}
        onChange={onChangeOption}
      />
    ),
    [submitOptionList, submitOption, t],
  );

  const addButtonComponent = useCallback(
    (questionNumber: number) => {
      const _onActionAdd = function (id: number, index: number) {
        onActionAdd(id, questionNumber);
      };
      const questionTypeList = map(QuestionType.Basic, item => ({
        ...item,
        name: t(item.name),
      }));
      return currentNumber < maxNumberOfQuestion ? (
        <AddButtonMultiple
          isSingle
          actionList={questionTypeList}
          classStyle="pull-right mt-5"
          iconClass="file-plus"
          title="Question"
          onAction={_onActionAdd}
        />
      ) : (
        <></>
      );
    },
    [onActionAdd, t, currentNumber, maxNumberOfQuestion],
  );
  const editActionButton = useCallback(
    (index: number) => (
      <PanelButtons isInPanel>
        {!!isEditing && (
          <IconButton
            className="display-inline-block pull-right mt-5 ml-5"
            iconName="pencil7"
            title={t('Edit')}
            onClick={onActionEdit(index)}
          />
        )}
      </PanelButtons>
    ),
    [onActionEdit, isEditing],
  );
  const columns = useMemo(
    () => tableColumns(t, addButtonComponent, editActionButton),
    [t, addButtonComponent, editActionButton],
  );

  const [{ sortKey, sortOrder }, setSort] = useState<{
    sortOrder: TSortOrder;
    sortKey: string;
  }>({
    sortOrder: 'asc',
    sortKey: '',
  });

  const onChangeSort = useCallback(
    (sortKey, sortOrder) => setSort({ sortKey, sortOrder }),
    [setSort],
  );

  const sortedItems = useMemo(() => {
    items = map(items, item => {
      item = item || {};
      const questionTitle = (item && item?.questionTitle) || '';
      const _questionTitle =
        questionTitle &&
        questionTitle
          .replace(/<[^>]+>/g, '')
          .replace(/\&nbsp;/gi, '')
          .trim();
      const plainString =
        questionTitle && questionTitle.includes('<img')
          ? t('Image')
          : t('Question');
      item.questionTitleDisplay = isEmpty(_questionTitle)
        ? plainString
        : _questionTitle;
      return item;
    });
    if (sortKey !== '') {
      const column = find(columns, { id: sortKey });
      return orderBy(
        items,
        item => {
          const value = getCellSortBy(item, column);
          return isString(value) ? value.toLowerCase().replace(' ', '') : value;
        },
        sortOrder,
      );
    }
    return items;
  }, [columns, sortOrder, items, sortKey]);
  const [tableItems, setTableItems] = useState<IEContentQuestion[]>(
    sortedItems,
  );
  useEffect(() => {
    setTableItems(sortedItems);
  }, [sortedItems]);
  const handleDragSortUpdate = useCallback(
    async (initialIndex, finalIndex) => {
      if (initialIndex === finalIndex) {
        return;
      }

      const old = [...sortedItems];

      const sequenceFrom = old[initialIndex].groupSequence;
      const sequenceTo = old[finalIndex].groupSequence;

      const _itemsUpdated = reorder(sortedItems, initialIndex, finalIndex);

      // Use this if want to assign sequence in table view (work for sequence sort only)
      //   const sortedGroupSequences = _itemsUpdated
      //   .map(item => item.groupSequence)
      //   .sort((a, b) => a - b);

      // const reassignedItems = _itemsUpdated.map((item, index) => ({
      //   ...item,
      //   groupSequence: sortedGroupSequences[index],
      // }));

      await onMove(sequenceFrom, sequenceTo, _itemsUpdated);
      return true;
    },
    [onMove, sortedItems],
  );

  const tableConfig = useMemo<ISimpleTableConfig<IEContentQuestion>>(
    () => ({
      columns,
      sort: { sortKey, sortOrder },
      getId: ({ groupSequence }) => groupSequence,
    }),
    [columns, sortKey, sortOrder],
  );

  const toggleIsReadOnly = useCallback(() => {
    setIsReadOnly(!isReadOnly);
  }, [isReadOnly]);

  const modelTitleComponent = useMemo(() => {
    const questionTypeTitle =
      BasicById[selectedQuestion?.questionType]?.name || '';
    let title = t('Add Question (#{questionTypeTitle})', {
      questionTypeTitle,
    });
    if (isReadOnly) {
      title = t('View Question (#{questionTypeTitle})', {
        questionTypeTitle,
      });
    } else if (!!selectedQuestion?.questionTypeId) {
      title = t('Edit Question (#{questionTypeTitle})', {
        questionTypeTitle,
      });
    }
    return (
      <>
        <div
          className={classNames(styles.questionModalTitleContainer, 'pr-10')}
        >
          <ArrowButtonsTitle
            currentRecord={selectedQuestion?.groupSequence || 0}
            setNewRecord={setNewRecord}
            title={title}
            totalRecord={currentNumber}
          />
          <PanelButton
            className="ml-5"
            icon={!isReadOnly ? 'eye' : 'pencil7'}
            title={!isReadOnly ? t('View Mode') : t('Edit Mode')}
            onClick={toggleIsReadOnly}
          />
        </div>
      </>
    );
  }, [
    toggleIsReadOnly,
    t,
    isReadOnly,
    selectedQuestion,
    BasicById,
    currentNumber,
    setNewRecord,
    startQuestion,
  ]);
  const quickAddComponent = useMemo(
    () =>
      isEditing ? (
        <a
          className={classNames(styles.quickAdd)}
          href="javascript:void(0)"
          onClick={showQuickAddModal}
        >
          {t('Quick Add')}
        </a>
      ) : (
        <></>
      ),
    [t, isEditing],
  );
  const renderSubmitSectionEmpty = useCallback(props => <></>, []);

  const handleSubmitDefault = useSubmitDefault({ submit: _onSubmitMultiple });

  const tableConfigPreviewQuestions = useMemo<ISimpleTableConfig>(
    () => ({
      columns: tableColumnsPreviewQuestions(t, false),
      getId: ({ questionTitleId }) => questionTitleId,
      sort: {
        sortKey: 'questionTitleId',
      },
    }),
    [t],
  );

  const tableConfigPreviewQuestionsPdf = useMemo<ISimpleTableConfig>(
    () => ({
      columns: tableColumnsPreviewQuestions(t, true),
      getId: ({ questionTitleId }) => questionTitleId,
      sort: {
        sortKey: 'questionTitleId',
      },
    }),
    [t],
  );

  const renderModalFooter = useCallback(
    () => (
      <>
        <hr className={styles.modalHr} />
        <RoundedGoBackButton onClick={handlePreviewQuestionsShowCancel}>
          {t('Close')}
        </RoundedGoBackButton>
      </>
    ),
    [handlePreviewQuestionsShowCancel, t],
  );

  const renderModalFooterPdf = useCallback(
    () => (
      <>
        <hr className={styles.modalHr} />
        <RoundedGoBackButton onClick={handlePreviewQuestionsShowPdfCancel}>
          {t('Close')}
        </RoundedGoBackButton>
      </>
    ),
    [handlePreviewQuestionsShowPdfCancel, t],
  );

  return (
    <>
      <div className="row">
        <div className="col-lg-12">
          <Strike
            isSubsection
            className="pull-left"
            title={`Questions (${currentNumber} out of ${maxNumberOfQuestion})`}
          />
          <div className="pull-right display-inline-flex">
            {addButtonList && <div>{addButtonList()}</div>}
            {previewButtonList && !isEditing && (
              <div>{previewButtonList()}</div>
            )}
          </div>
        </div>
      </div>
      {items?.length > 0 && (
        <>
          <StaticTable
            isDraggable
            isDraggableHover
            className={styles.questionTable}
            config={tableConfig}
            items={tableItems}
            onChangeSort={onChangeSort}
            onDragSortUpdate={handleDragSortUpdate}
            onRowClick={gotoQuestionDetails}
          />
          {addButtonList && (
            <div className="mt-10 text-center">{addButtonList()}</div>
          )}
        </>
      )}
      <Modal
        className={classNames(styles.modelBox)}
        size="lg"
        title={modelTitleComponent}
        visible={!!selectedQuestion?.questionType}
        onClose={handleCancel}
      >
        <QuestionFormElements
          addNewButtonList={addNewButtonList}
          addSubmitOption={addSubmitOption}
          clobsPreviewData={clobsPreviewData}
          entity={selectedQuestion}
          hasKeyword={hasKeyword}
          hasRenderedPreview={hasRenderedPreview}
          isKeywordRequired={isKeywordRequired}
          isReadOnly={isReadOnly}
          maxTagLength={maxTagLength}
          quickAddComponent={quickAddComponent}
          suggestions={suggestions}
          onCancel={handleCancel}
          onDelete={_onDelete}
          onSubmit={_onSubmitMain}
          onSubmitClose={_onSubmitClose}
        />
      </Modal>
      <Modal
        size="sm"
        title={t('Quick Add')}
        visible={quickAddShow}
        onClose={handleQuickAddCancel}
      >
        <QuestionQuickAdd
          onCancel={handleQuickAddCancel}
          onSubmit={handleQuickAddSubmit}
        />
      </Modal>
      <Modal
        className={classNames(styles.agModelBox)}
        size="lg"
        title={t('Auto Generate Question')}
        visible={quickAutoGenerate}
        onClose={handleAutoGenerateCancel}
      >
        <EntityForm
          entity={{} as IBasicEntity}
          entityName="QuestionAutoGenerate"
          submitSectionRenderer={renderSubmitSectionEmpty}
          onSubmit={handleSubmitDefault as any}
        >
          <QuestionAutoGenerate
            textList={textList}
            onCancel={handleAutoGenerateCancel}
            onSubmitMultiple={_onSubmitMultiple}
          />
        </EntityForm>
      </Modal>
      <Modal
        renderFooter={renderModalFooter}
        size="lg"
        title={t('Preview Questions')}
        visible={previewQuestionsShow}
        onClose={handlePreviewQuestionsShowCancel}
      >
        <div className={styles.tableContainer}>
          <StaticTable config={tableConfigPreviewQuestions} items={items} />
        </div>
      </Modal>
      <Modal
        renderFooter={renderModalFooterPdf}
        size="xl"
        title={t('PDF')}
        visible={previewQuestionsShowPdf}
        onClose={handlePreviewQuestionsShowPdfCancel}
      >
        {pdfUrlLoading ? (
          <div className="text-center">
            <Spinner />
          </div>
        ) : (
          <div className={styles.tableContainer}>
            <iframe
              className="h-100-p w-100"
              src={pdfUrl}
              title="PDF Preview"
            />
          </div>
        )}
      </Modal>
      <div ref={divRef} className={styles.posFixed}>
        <StaticTable
          config={tableConfigPreviewQuestionsPdf}
          contentRef={tableRef}
          items={items}
        />
      </div>
    </>
  );
};

export default QuestionTable;

const reorder = (arr, oldIndex: number, newIndex: number) => {
  const result = [...arr];
  if (newIndex >= result.length) {
    let k = newIndex - result.length + 1;
    while (k--) {
      result.push(undefined);
    }
  }
  result.splice(newIndex, 0, result.splice(oldIndex, 1)[0]);
  return result;
};
