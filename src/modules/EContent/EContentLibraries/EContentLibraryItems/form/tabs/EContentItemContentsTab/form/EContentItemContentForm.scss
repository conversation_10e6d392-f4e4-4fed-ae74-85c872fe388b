.arrowContainer {
  display: flex;
  justify-content: space-between;
  &:hover {
    div .arrow {
      opacity: 1;
    }
  }
}

.hasPrev {
  h5 {
    transition: padding-left 0.2s ease-in;
  }

  &:hover {
    h5 {
      padding-left: 12px;
    }
  }
}
.wrapper {
  border: 1px solid #eeeded;
  padding: 10px 20px;
  margin-right: 0 !important;
}
// .topFlex {
//   .content,
//   .framework {
//     > h6 {
//       margin-left: -10px;
//     }
//   }
// }

.attachmentsGrid {
  :global(.attached-area) {
    display: flex;
    flex-wrap: wrap;
  }
}
.submitBox {
  margin-top: 10px;
  clear: both !important;
  border-top: 1px solid #f7f7f7;
  padding-top: 10px;
  margin-left: 0px;
  margin-right: 0px;
}

@media screen and (max-width: 769px) {
  .submitBox {
    margin-bottom: 40px !important;
    margin-top: 5px !important;
  }
}
@media all and (min-width: 1024px) {
  .topFlex {
    display: flex;
    width: 100%;
    flex-wrap: wrap;
    flex-direction: row;
    margin: 0;

    .content {
      display: flex;
      flex-direction: column;
      padding: 0;

      .wrapper {
        height: 100%;
      }
    }

    .framework {
      display: flex;
      flex-direction: column;
      padding-right: 0;
      padding-left: 20px;

      .wrapper {
        height: 100%;
      }
    }
  }
}
.width100 {
  width: 100px;
}
.addWrap {
  margin-left: 10px;
  float: left;
}

.removeIcon {
  color: red;
  margin-top: -25px;
  float: right;
}
.bgGray {
  background: #f6f6f6;
}
.center {
  text-align: center;
}
.roundButton {
  i {
    font-size: 12px !important;
    font-weight: bold !important;
  }
  :global(.legitRipple) {
    margin-left: 10px;
    margin-right: 10px;
  }
}
.transformModelBox {
  @media screen and (max-width: 500px) {
    :global(.modal-dialog) {
      max-width: 98% !important;
    }
  }
  @media screen and (min-width: 500px) {
    :global(.modal-dialog) {
      max-width: 90% !important;
    }
  }
}
.tabContainer :global(.nav-tabs) {
  margin-top: 5px;
}
.tabContainer :global(.nav) {
  margin-top: 5px;
}
.tabContainerML :global(.nav) {
  left: -20px;
}

.textContentContainer {
  overflow: hidden;
  transition: 'max-height 0.5s ease';
}

.modalWrapper {
  :global(.swal2-actions) {
    justify-content: right !important;
  }
}
.mouseButtonContainer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
@media (max-width: 1024px) {
  .footerMobileMargin {
      margin-bottom: 10px !important;
  }
  .mobileFrameworkDiv {
    margin-left: 0 !important;
  }
}

.pointsLabel {
  width: 36%;
  float: right;
}

@media (max-width: 1024px) {
  .pointsLabel {
    display: none !important;
  }
}
.mb60 {
  margin-bottom: 60px !important;
}
