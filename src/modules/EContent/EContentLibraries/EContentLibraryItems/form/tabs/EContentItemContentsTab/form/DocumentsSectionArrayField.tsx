import React, { useCallback, useEffect, useMemo } from 'react';
import { get } from 'lodash';
import classnames from 'classnames';

import useT from '../../../../../../../../common/components/utils/Translations/useT';
import AttachmentFieldWithPreview from '../../../../../../../../common/components/containers/EntityForm/fields/AttachmentFieldWithPreview';
import { ALL_DOCUMENTS } from '../../../../../../../../common/propTypes';
import useCurrentUser from '../../../../../../../../common/data/hooks/useCurrentUser';
import { E_CONTENT_CONTENT_FILE } from '../../../../../../../../fsCategories';
import { OPTIONAL, TRuleType } from '../../../../../../../../model/RuleType';
import AttachmentItemCard from '../../../../../../../../common/components/controls/Attachments/listItems/AttachmentItemCard';
import useEntityFormContext from '../../../../../../../../common/components/containers/EntityForm/internal/useEntityFormContext';
import styles from './EContentItemContentForm.scss';
import { IEContentResourceCategory } from '../../../../../../../../common/abstract/EContent/IEContentResourceXAttribute';

const BYTES_IN_MEGABYTE = 1048576;
const FIELD_NAME = 'cookedClobs.document';

export interface IDocumentsSectionArrayField {
  hasItems: (name: string, currentItems: [any]) => void;
  minNumberOfDocuments: number;
  maxNumberOfDocuments: number;
  maxSizeOfEachDocument: number;
  documentCaptionRule?: TRuleType;
  hasCaption: boolean;
  maxTagLength?: number;
  isKeywordRequired?: boolean;
  hasKeyword?: boolean;
  suggestions?: string[];
  isContentCategoryEnabled: boolean;
  resourceCategoriesOptions: IEContentResourceCategory[];
  selectedTabId?: number | string;
}

const DocumentsSectionArrayField: React.FC<IDocumentsSectionArrayField> = ({
  hasItems,
  minNumberOfDocuments,
  maxNumberOfDocuments,
  maxSizeOfEachDocument,
  documentCaptionRule = OPTIONAL.value,
  hasCaption = false,
  maxTagLength,
  isKeywordRequired,
  hasKeyword,
  suggestions,
  isContentCategoryEnabled,
  resourceCategoriesOptions,
  selectedTabId,
}) => {
  const t = useT();

  const maxSizeInBytes = useMemo(
    () => maxSizeOfEachDocument * BYTES_IN_MEGABYTE,
    [maxSizeOfEachDocument],
  );

  const {
    me: { tenantId, organisationGroupId },
  } = useCurrentUser();

  const attachmentItemComponent = useCallback(
    props => (
      <AttachmentItemCard
        hasSequence
        documentCaptionRule={documentCaptionRule}
        hasCaption={hasCaption}
        hasKeyword={hasKeyword}
        isContentCategoryEnabled={isContentCategoryEnabled}
        isKeywordRequired={isKeywordRequired}
        maxTagLength={maxTagLength}
        resourceCategoriesOptions={resourceCategoriesOptions}
        selectedTabId={selectedTabId}
        suggestions={suggestions}
        {...props}
      />
    ),
    [
      isContentCategoryEnabled,
      resourceCategoriesOptions,
      hasCaption,
      documentCaptionRule,
      maxTagLength,
      isKeywordRequired,
      hasKeyword,
      suggestions,
      selectedTabId,
    ],
  );

  const { values } = useEntityFormContext();

  const currentNumber = useMemo(() => get(values, `${FIELD_NAME}.length`, 0), [
    values,
  ]);

  const currentItems = useMemo(() => get(values, `${FIELD_NAME}`, []), [
    values,
  ]);

  useEffect(() => {
    hasItems && hasItems('document', currentItems);
  }, [hasItems, currentItems]);

  const label = useMemo(() => {
    let heading = 'Documents';
    if (maxNumberOfDocuments > 1) {
      heading = `${heading} (${currentNumber} out of ${maxNumberOfDocuments})`;
    }
    return heading;
  }, [maxNumberOfDocuments, currentNumber]);

  return (
    <div className={classnames('row', styles.attachmentsGrid)}>
      <AttachmentFieldWithPreview
        isDownloadable
        isSubsection
        addButtonTitle={t('Add Document')}
        allowedFileTypes={ALL_DOCUMENTS}
        allowedNumberOfFiles={maxNumberOfDocuments}
        attachmentItemComponent={attachmentItemComponent}
        cardsInRow={3}
        categoryKey={E_CONTENT_CONTENT_FILE}
        columns={1}
        hasErrorMessage={false}
        hasPhotoRecorder={false}
        hasRoutes={false}
        maxDescriptionLength={400}
        maxSize={maxSizeInBytes}
        name={FIELD_NAME}
        organisationGroupId={organisationGroupId}
        required={minNumberOfDocuments > 0}
        strikeTitle={t(label)}
        tenantId={tenantId}
      />
    </div>
  );
};

export default DocumentsSectionArrayField;
