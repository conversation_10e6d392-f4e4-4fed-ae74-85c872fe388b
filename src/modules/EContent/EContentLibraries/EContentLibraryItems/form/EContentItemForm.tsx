import React, { useCallback, useContext, useEffect, useMemo } from 'react';
import { useLocation, useRouteMatch } from 'react-router-dom';
import { find, includes, get, isEmpty, filter, flatMap } from 'lodash';
import { useQuery } from 'react-apollo';
import { ApolloQueryResult } from 'apollo-client';

import EContentItemDetailsTab from './tabs/EContentItemDetailsTab';
import EContentItemContentsTab from './tabs/EContentItemContentsTab';
import useT from '../../../../../common/components/utils/Translations/useT';
import Tabs from '../../../../../common/components/utils/Tabs';
import IEContentItem from '../../../../../common/abstract/EContent/IEContentItem';
import IEContentResource from '../../../../../common/abstract/EContent/IEContentResource';
import BreadcrumbsContext from '../../../../../common/components/utils/Breadcrumbs/BreadcrumbsContext';
import { Deleted } from '../../../../../model/StatusWithDraft';
import EContentItemTranslationSupportTab from './tabs/EContentItemTranslationSupportTab';
import { TRANSLATION_SUPPORT } from '../../../../Organization/OrganisationGroups/EContentSetup/model/EContentResourceXAttributeFeatureOptions';
import attributeTypes from '../../../../../model/EContentResourceXAttributeTypes';
import eContentItemsTranslationSupportLanguages from '../../../../../common/data/eContent/eContentItemsTranslationSupportLanguages.graphql';
import getGqlOperationName from '../../../../../common/utils/getGqlOperationName';
import SpinnerError from '../../../../../common/components/utils/SpinnerError';

export interface IEContentItemForm {
  entity: IEContentItem;
  onGoBack: () => void;
  onDelete?: () => void | Promise<void>;
  onSubmit: (values: IEContentItem) => void;
  libraryId?: number;
  libraryName?: string;
  selectedResources?: Partial<IEContentResource>[];
  goToCreateItem: () => void;
  usedKeywords?: string[];
  fromPage?: string;
  refetchTranslationSupport?: () => Promise<ApolloQueryResult<object>>;
  floatButton?: any;
}

const EContentItemForm: React.FC<IEContentItemForm> = ({
  entity,
  selectedResources,
  onDelete,
  onGoBack,
  ...props
}) => {
  const t = useT();
  const { url } = useRouteMatch();
  const { pathname } = useLocation();

  const { breadcrumbs, updateAnchor } = useContext(BreadcrumbsContext);

  const isNew = useMemo(() => !entity?.id, [entity]);

  const isTranslationSupportEnabled = useMemo(
    () =>
      entity?.resource &&
      entity?.resource.attributes &&
      entity?.resource.attributes.find(
        x =>
          x.attributeId === attributeTypes.BasicByName.translationSupport?.id,
      )?.isChecked,
    [entity],
  );

  const { data, loading, refetch: refetchTranslationSupport } = useQuery(
    eContentItemsTranslationSupportLanguages,
    {
      variables: { itemId: entity.id },
    },
  );

  const contentLanguages = useMemo(
    () =>
      get(
        data,
        getGqlOperationName(eContentItemsTranslationSupportLanguages),
        [],
      ),
    [data],
  );

  const hasNonUniqueLanguageContent = useMemo(
    () => find(contentLanguages, { nonUniqueLanguageContent: true }),
    [contentLanguages],
  );

  const hasNonUniqueGroupSequence = useMemo(
    () => find(contentLanguages, { nonUniqueGroupSequence: true }),
    [contentLanguages],
  );

  const translationSupportTooltip = useMemo(() => {
    if (!contentLanguages.length) return t('There are no texts');
    else if (hasNonUniqueLanguageContent)
      return t('There are multiple contents with the same language');
    else if (hasNonUniqueGroupSequence) {
      const filtered = filter(contentLanguages, {
        nonUniqueGroupSequence: true,
      });
      if (filtered.length === 1 && filtered[0].heading.length === 1) {
        return t(
          `Text sequences related to '${filtered[0].heading}' in ${filtered[0].name} are not unique`,
        );
      }
      const mapped = flatMap(filtered, item =>
        item.heading.map(heading => `'${heading}' in ${item.name}`),
      );
      return t(
        `Text sequences related to the below contents are not unique:\n#{contents}`,
        {
          contents: mapped.join('\n'),
        },
      );
    }
    return t(TRANSLATION_SUPPORT.name);
  }, [
    hasNonUniqueLanguageContent,
    hasNonUniqueGroupSequence,
    t,
    contentLanguages,
  ]);

  useEffect(() => {
    const breadcrumb = find(breadcrumbs, { route: url });
    const divider = '\u203a\u00a0';
    const resourceName = isNew ? 'New Resource' : entity?.resource?.name;
    let _title: string | undefined = get(entity, 'name', '');
    _title = isEmpty(_title) ? breadcrumb && breadcrumb.title : _title;
    if (breadcrumb && !includes(breadcrumb.title, divider) && resourceName) {
      const title = `${resourceName} ${divider} ${_title}`;
      updateAnchor({ ...breadcrumb, title });
    }
  }, [entity, updateAnchor, url, isNew, breadcrumbs]);

  const handleDelete = useCallback(async () => {
    onDelete && (await onDelete());
    onGoBack();
  }, [onDelete, onGoBack]);

  const isContentTab = useMemo(() => {
    if (pathname.endsWith('content')) {
      return true;
    }
    return false;
  }, [pathname]);

  const isContentsForm = useMemo(() => {
    if (
      pathname === `${url}/details` ||
      pathname === `${url}/contents-list` ||
      pathname.includes('/translation-support')
    ) {
      return false;
    }
    return true;
  }, [pathname, url]);

  return (
    <SpinnerError loading={loading}>
      <Tabs
        additionalClasses={['mobileSpacing']}
        isHidden={isContentsForm}
        tabStyle="no-margin-bottom"
      >
        <Tabs.Tab route="details" title={t('Details')}>
          <EContentItemDetailsTab
            entity={entity}
            selectedResources={selectedResources}
            onDelete={
              !entity?.contentsCount && entity.status !== Deleted.value
                ? handleDelete
                : undefined
            }
            onGoBack={onGoBack}
            {...props}
          />
        </Tabs.Tab>
        <Tabs.Tab
          default={!isNew}
          disabled={isNew}
          route="contents-list"
          title={t('Contents List')}
        >
          <EContentItemContentsTab
            entity={entity}
            refetchTranslationSupport={refetchTranslationSupport}
            onDelete={
              !entity?.contentsCount && entity.status !== Deleted.value
                ? handleDelete
                : undefined
            }
            onGoBack={onGoBack}
            {...props}
            floatButton={isContentTab}
          />
        </Tabs.Tab>
        {isTranslationSupportEnabled && (
          <Tabs.Tab
            disabled={
              !contentLanguages ||
              !contentLanguages.length ||
              hasNonUniqueLanguageContent ||
              hasNonUniqueGroupSequence
            }
            route="translation-support"
            title={t(TRANSLATION_SUPPORT.name)}
            tooltip={translationSupportTooltip}
          >
            <EContentItemTranslationSupportTab
              contentLanguagesCount={contentLanguages.length}
              entity={entity}
              onGoBack={onGoBack}
              {...props}
            />
          </Tabs.Tab>
        )}
      </Tabs>
    </SpinnerError>
  );
};

export default EContentItemForm;
