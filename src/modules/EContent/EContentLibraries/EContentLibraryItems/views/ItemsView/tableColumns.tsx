/* eslint-disable react/display-name,react/no-multi-comp, react/prop-types */
import React from 'react';
import { Link } from 'react-router-dom';
import classNames from 'classnames';

import { ISimpleTableConfigColumn } from '../../../../../../common/propTypes';
import highLightText from '../../../../../../common/highlightTextHelper';

import { format, Edana4 } from '../../../../../../common/utils/dateTime';
import StatusWithDraft from '../../../../../../model/StatusWithDraft';
import LanguageCell, { ILanguageCell } from '../../LanguagesCell';
import IEContentItem from '../../../../../../common/abstract/EContent/IEContentItem';
import styles from './tableColumns.scss';

export default function cookColumns(t, libraryId) {
  const OPTIONS: ISimpleTableConfigColumn<IEContentItem> = {
    id: 'options',
    type: 'options',
  };
  const NAME: ISimpleTableConfigColumn<IEContentItem> = {
    id: 'name',
    title: 'Item',
    sortable: true,
    type: 'always-display',
    clickable: true,
    fieldGetter: ({ name }) => name || '-',

    cellRenderer: ({ id, name }) => {
      if (!libraryId) return <span>{name || '-'}</span>;
      const pathto = {
        pathname: `/e-content/libraries/edit/${libraryId}/edit/${id}/contents-list`,
        fromPage: 'itemview',
      };
      return (
        <Link className={classNames(styles.color)} to={pathto}>
          {name || '-'}
        </Link>
      );
    },
  };

  const RESOURCE: ISimpleTableConfigColumn<IEContentItem> = {
    id: 'resource',
    title: 'Resource',
    sortable: true,
    type: 'always-display',
    fieldGetter: ({ resource }) => resource?.name || '-',
  };

  const LANGUAGES: ISimpleTableConfigColumn<IEContentItem> = {
    id: 'languages',
    title: 'Languages',
    sortable: true,
    cellRenderer: node => <LanguageCell node={node as ILanguageCell['node']} />,
  };

  const CONTENTS: ISimpleTableConfigColumn<IEContentItem> = {
    id: 'contents',
    title: 'Contents',
    sortable: true,
    type: 'always-display',
    fieldGetter: ({ contentsCount }) => contentsCount || '0',
  };

  const UPDATED_BY: ISimpleTableConfigColumn<IEContentItem> = {
    id: 'updated_by',
    title: 'Updated By',
    sortable: true,
    type: 'always-display',
    fieldGetter: ({ updatedByPerson }) =>
      `${updatedByPerson.firstName} ${updatedByPerson.lastName}` || '-',
    cellRenderer: ({ updatedByPerson }, searchQuery) => {
      const name =
        `${updatedByPerson.firstName} ${updatedByPerson.lastName}` || '';
      return <div>{highLightText(name, searchQuery)}</div>;
    },
  };

  const UPDATED_AT: ISimpleTableConfigColumn<IEContentItem> = {
    id: 'updated_at',
    title: 'Updated Date',
    sortable: true,
    fieldGetter: ({ updatedAt }) =>
      updatedAt ? format(updatedAt, Edana4) : '-',
  };

  const STATUS: ISimpleTableConfigColumn<IEContentItem> = {
    id: 'status',
    title: 'Status',
    sortable: true,
    type: 'always-display',
    fieldGetter: ({ status }) => t(StatusWithDraft.BasicByValue[status]?.name),
  };

  return [
    OPTIONS,
    NAME,
    RESOURCE,
    LANGUAGES,
    CONTENTS,
    UPDATED_BY,
    UPDATED_AT,
    STATUS,
  ];
}
