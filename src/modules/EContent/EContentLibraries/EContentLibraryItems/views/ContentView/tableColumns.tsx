/* eslint-disable react/display-name,react/no-multi-comp, react/prop-types, react/forbid-dom-props */
import React from 'react';
import { Link } from 'react-router-dom';
import classNames from 'classnames';

import { ISimpleTableConfigColumn } from '../../../../../../common/propTypes';
import highLightText from '../../../../../../common/highlightTextHelper';

import { format, Edana4 } from '../../../../../../common/utils/dateTime';
import StatusWithDraft from '../../../../../../model/StatusWithDraft';
import LanguageCell from '../../LanguagesCell';
import { cookEContentClobs } from '../../form/tabs/EContentItemContentsTab/form/EContentItemContentForm';
import IEContentContent from '../../../../../../common/abstract/EContent/IEContentContent';
import ILanguage from '../../../../../../common/abstract/ILanguage';
import styles from './tableColumns.scss';

export default function cookColumns(t, parentUrl) {
  const OPTIONS: ISimpleTableConfigColumn<IEContentContent> = {
    id: 'options',
    type: 'options',
  };

  const ITEM: ISimpleTableConfigColumn<IEContentContent> = {
    id: 'item',
    title: 'Item',
    sortable: true,
    type: 'always-display',
    clickable: true,
    cellRenderer: ({ item }) => {
      const pathto = {
        pathname: `${parentUrl}/edit/${item?.id}/contents-list`,
        fromPage: 'contentview',
      };
      return (
        <Link
          className={classNames(styles.color)}
          to={pathto}
          // eslint-disable-next-line react/jsx-no-bind
          onClick={e => e.stopPropagation()}
        >
          {item?.name || '-'}
        </Link>
      );
    },
  };

  const RESOURCE: ISimpleTableConfigColumn<IEContentContent> = {
    id: 'resource',
    title: 'Resource',
    sortable: true,
    type: 'always-display',
    fieldGetter: ({ item }) => item?.resource?.name || '-',
  };

  const LANGUAGE: ISimpleTableConfigColumn<IEContentContent> = {
    id: 'language',
    title: 'Language',
    sortable: true,
    cellRenderer: ({ language }) => (
      <LanguageCell node={{ languages: [language as ILanguage] }} />
    ),
  };

  const CONTENT: ISimpleTableConfigColumn<IEContentContent> = {
    id: 'content',
    title: 'Content',
    sortable: true,
    type: 'always-display',
    clickable: true,
    cellRenderer: ({ clobs, item }) => {
      if (!clobs) {
        return '-';
      }
      const cookedClobs = cookEContentClobs(clobs, item.resource?.attributes);
      const pathto = {
        pathname: `${parentUrl}/edit/${item.id}/contents-list/edit/${cookedClobs?.heading?.contentId}/content`,
        fromPage: 'contentview',
      };
      return (
        <a
          className={classNames(styles.color)}
          href={pathto.pathname}
          // eslint-disable-next-line react/jsx-no-bind
          onClick={e => {
            e.preventDefault();
          }}
        >
          {cookedClobs?.heading?.content}
        </a>
      );
    },
  };

  const UPDATED_BY: ISimpleTableConfigColumn<IEContentContent> = {
    id: 'updated_by',
    title: 'Updated By',
    sortable: true,
    type: 'always-display',
    fieldGetter: ({ updatedByPerson }) =>
      `${updatedByPerson.firstName} ${updatedByPerson.lastName}` || '-',
    cellRenderer: ({ updatedByPerson }, searchQuery) => {
      const name =
        `${updatedByPerson.firstName} ${updatedByPerson.lastName}` || '';
      return <div>{highLightText(name, searchQuery)}</div>;
    },
  };

  const UPDATED_AT: ISimpleTableConfigColumn<IEContentContent> = {
    id: 'updated_at',
    title: 'Updated Date',
    sortable: true,
    fieldGetter: ({ updatedAt }) =>
      updatedAt ? format(updatedAt, Edana4) : '-',
  };

  const STATUS: ISimpleTableConfigColumn<IEContentContent> = {
    id: 'status',
    title: 'Status',
    sortable: true,
    type: 'always-display',
    fieldGetter: ({ status }) => t(StatusWithDraft.BasicByValue[status]?.name),
  };

  return [
    OPTIONS,
    ITEM,
    RESOURCE,
    CONTENT,
    LANGUAGE,
    UPDATED_BY,
    UPDATED_AT,
    STATUS,
  ];
}
