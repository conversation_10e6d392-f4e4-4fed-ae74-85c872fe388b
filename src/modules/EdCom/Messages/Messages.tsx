import { includes, map } from 'lodash';
import moment, { Moment } from 'moment';
import React, { FC, useCallback, useContext } from 'react';
import { IModuleComponentProps } from '../../../common/abstract/IModuleProps';
import IMessage from '../../../common/abstract/Messages/IMessage';
import TMessageType from '../../../common/abstract/Messages/TMessageType';
import { OrganisationsFilterContext } from '../../../common/components/containers/OrganisationsFilterContextProvider';
import { getMessageDeleteText } from '../../../common/components/forms/MessageItems/hooks/useInputPlaceholder';
import useIsMessageMine from '../../../common/components/forms/MessageItems/hooks/useIsMessageMine';
import UnreadMessagesCount from '../../../common/components/other/UnreadMessagesCount';
import useT from '../../../common/components/utils/Translations/useT';
import useCurrentUser from '../../../common/data/hooks/useCurrentUser';
import usePersonEntityTypeGroupSelector from '../../../common/data/hooks/usePersonEntityTypeGroupSelector';
import {
  format,
  TransportDateTimeFormat,
} from '../../../common/utils/dateTime';
import MessageType, {
  BROADCAST,
  CHANNEL,
  CHAT,
  isBroadcastOrChannel,
  isChat,
  isPrivateChat,
  PRIVATE_CHAT,
} from '../../../model/MessageType';
import { ACTIVE } from '../../PeopleRecords/PersonDatabase/PersonInfo/PersonInfoTabs/General/Contracts/model/ContractStatus';
import EdComCrud from '../EdComCrud';
import IEdComRightEditTab from '../EdComCrud/abstract/IEdComRightEditTab';
import EdComEntityFilter from '../EdComCrud/EdComLeftTabs/filters/EdComEntityFilter';
import IMessageFilters from './abstract/IMessageFilters';
import IMessageListVariables from './abstract/IMessageListVariables';
import IMessagesContactFilters from './abstract/IMessagesContactFilters';
import IMessagesContactVariables from './abstract/IMessagesContactVariables';
import IMessageTemp from './abstract/IMessageTemp';
import TMessageHistory from './abstract/TMessageHistory';
import MessageCard from './card/MessageCard';
import createOrUpdateMessageParticipantsMutation from './data/createOrUpdateMessageParticipants.graphql';
import createOrUpdateMessageSettingsMutation from './data/createOrUpdateMessageSettings.graphql';
import deleteMessageMutation from './data/deleteMessage.graphql';
import messageQuery from './data/message.graphql';
import messageContactsQuery from './data/messageContacts.graphql';
import messageContactsCountQuery from './data/messageContactsCount.graphql';
import messagePrivateChatQuery from './data/messagePrivateChat.graphql';
import messagesQuery from './data/messages.graphql';
import messagesCountQuery from './data/messagesCount.graphql';
import MessageContactTypeFilter from './filters/MessageContactTypeFilter';
import MessageHistoryFilter from './filters/MessageHistoryFilter';
import MessageTypeFilter from './filters/MessageTypeFilter';
import MessagesWrapper from './MessagesWrapper';
import MessageContactType from './model/MessageContactType';
import { LAST_7_DAYS, TODAY } from './model/MessageHistory';
import { getMessageDeleteMessage } from './tabs/hooks/useDeleteOrLeftChat';
import MessagesItemsTab from './tabs/MessagesItemsTab';
import MessagesParticipantsTab from './tabs/MessagesParticipantsTab';
import MessagesSettingsTab from './tabs/MessagesSettingsTab';
import UnreadMessagesCountHook from '../../../common/components/other/UnreadMessagesCount/UnreadMessagesCountHook';

const now = moment();

const Messages: FC<IModuleComponentProps> = ({ title }) => {
  const t = useT();
  const { me } = useCurrentUser();

  const { selectedOrganisations } = useContext(OrganisationsFilterContext);

  const {
    loading,
    personEntityTypeGroupSelector,
  } = usePersonEntityTypeGroupSelector();

  const cookQueryVariables = useCallback<
    (filters: IMessageFilters) => IMessageListVariables
  >(
    ({ searchQuery, messageType, history }) => ({
      searchQuery,
      types: mapMessageType(messageType),
      organisationIds: map(selectedOrganisations, 'id'),
      lastMessageFrom: getLastMessageFrom(history, now),
    }),
    [selectedOrganisations],
  );

  const deleteTitleText = useCallback<(item: IMessage) => string>(
    item => getMessageDeleteMessage(t, item),
    [t],
  );

  const deleteCheckboxText = useCallback<
    (item: IMessage) => string | undefined
  >(
    item =>
      isChat(item) || isPrivateChat(item)
        ? getMessageDeleteText(t, item)
        : undefined,
    [t],
  );

  const cookDeleteVariables = useCallback<
    (item: IMessage, checkboxValue: boolean) => object
  >(
    (item, checkboxValue) => ({
      id: item.id,
      params: {
        forEveryone: checkboxValue || isBroadcastOrChannel(item),
      },
    }),
    [],
  );

  const isMessageMine = useIsMessageMine();

  const canDelete = useCallback<(item: IMessage) => boolean>(
    item =>
      item.status === ACTIVE.value &&
      isMessageMine(item) &&
      (isChat(item) || isBroadcastOrChannel(item)),
    [isMessageMine],
  );

  const tabs = useCallback<(item?: IMessage) => IEdComRightEditTab<IMessage>[]>(
    item => {
      const tabs: IEdComRightEditTab<IMessage>[] = [];

      if (isChat(item) || (isBroadcastOrChannel(item) && isMessageMine(item))) {
        tabs.push({
          route: 'participants',
          title: 'Participants',
          submitMutation: createOrUpdateMessageParticipantsMutation,
          element: <MessagesParticipantsTab />,
          goNextOnSubmit: true,
        });
      }

      tabs.push({
        route: 'settings',
        title: 'Settings',
        submitMutation: createOrUpdateMessageSettingsMutation,
        element: <MessagesSettingsTab />,
      });

      if (!item || item?.id) {
        tabs.push({
          route: 'messages',
          title: 'Messages',
          rightLabel: (
            <UnreadMessagesCountHook messageId={item?.id as number} />
          ),
          element: <MessagesItemsTab />,
          getItemTitle,
          isDefault: true,
        });
      }

      return tabs;
    },
    [isMessageMine],
  );

  return (
    <EdComCrud<
      IMessage,
      IMessageFilters,
      IMessageListVariables,
      IMessagesContactFilters,
      IMessagesContactVariables,
      IMessageTemp
    >
      contacts={{
        filterBar: {
          defaultValue: {
            searchQuery: '',
            types: MessageContactType.DefaultValues,
            entityTypeIds: map(personEntityTypeGroupSelector, 'id'),
          },
          components: {
            entityTypeIds: EdComEntityFilter,
            types: MessageContactTypeFilter,
          },
          loading,
        },
        gql: {
          itemsQuery: messageContactsQuery,
          itemsCountQuery: messageContactsCountQuery,
          setItemQuery: messagePrivateChatQuery,
        },
      }}
      getName={getItemTitle}
      gql={{
        cookQueryVariables,
        itemsQuery: messagesQuery,
        itemsCountQuery: messagesCountQuery,
        itemQuery: messageQuery,

        deleteMutation: deleteMessageMutation,
        cookDeleteVariables,
      }}
      left={{
        addOptionsTitle: 'Message',
        addOptions: [
          {
            title: t(CHAT.name),
            defaultEntity: {
              type: CHAT.value,
              createdBy: me.id,
              isOwner: true,
            },
          },
          {
            title: t(BROADCAST.name),
            defaultEntity: {
              type: BROADCAST.value,
              createdBy: me.id,
              isOwner: true,
            },
          },
          {
            title: t(CHANNEL.name),
            defaultEntity: {
              type: CHANNEL.value,
              createdBy: me.id,
              isOwner: true,
            },
          },
        ],
        list: {
          ItemBodyComponent: MessageCard,
          deleteCheckboxText,
          canDelete,
          deleteTitleText,
        },
        filterBar: {
          components: {
            messageType: MessageTypeFilter,
            history: MessageHistoryFilter,
          },
          defaultValue: {
            searchQuery: '',
            history: LAST_7_DAYS.value,
            messageType: MessageType.DefaultValue,
          },
        },
        Wrapper: MessagesWrapper,
      }}
      right={{
        tabs,
      }}
      title={title}
    />
  );
};

export default Messages;

const getLastMessageFrom = (
  history: TMessageHistory,
  now: Moment,
): string | undefined => {
  if (history === LAST_7_DAYS.value) {
    return format(moment(now).subtract(1, 'week'), TransportDateTimeFormat);
  } else if (history === TODAY.value) {
    return format(moment(now).subtract(1, 'days'), TransportDateTimeFormat);
  }
  return undefined;
};

const mapMessageType = (messageType: TMessageType[]): TMessageType[] =>
  includes(messageType, CHAT.value)
    ? [...messageType, PRIVATE_CHAT.value]
    : messageType;

const getItemTitle = ({ recipientPerson, name, type }: IMessage) =>
  recipientPerson && isPrivateChat({ type })
    ? `${recipientPerson.firstName} ${recipientPerson.lastName}`
    : name;
