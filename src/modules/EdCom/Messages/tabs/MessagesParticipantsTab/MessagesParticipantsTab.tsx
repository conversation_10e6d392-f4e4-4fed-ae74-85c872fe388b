import { map, unionBy } from 'lodash';
import React, { FC, useCallback, useMemo, useState } from 'react';
import IRecipientMinimal from '../../../../../common/abstract/EdCom/IRecipientMinimal';
import IMessage from '../../../../../common/abstract/Messages/IMessage';
import EntityForm from '../../../../../common/components/containers/EntityForm';
import { IBasicEntity } from '../../../../../common/components/containers/EntityForm/EntityForm';
import useT from '../../../../../common/components/utils/Translations/useT';
import useCurrentUser from '../../../../../common/data/hooks/useCurrentUser';
import { isChannel } from '../../../../../model/MessageType';
import useEdComRightForm from '../../../EdComCrud/context/EdComRightForm';
import useEdComSelectedItem from '../../../EdComCrud/context/EdComSelectedItem';
import useEdComTempItem from '../../../EdComCrud/context/EdComTempItem';
import IMessageParticipantsInput from '../../abstract/IMessageParticipantsInput';
import IMessageTemp from '../../abstract/IMessageTemp';
import MessagesParticipantsTabBody from './MessagesParticipantsTabBody';
import useIsParticipantsReadonly from './useIsParticipantsReadonly';

const MessagesParticipantsTab: FC = () => {
  const t = useT();
  const { me } = useCurrentUser();

  const { selectedItem } = useEdComSelectedItem<IMessage>();
  const { tempData, setTempData } = useEdComTempItem<IMessageTemp>();
  const {
    onSubmit,
    onGoNext,
    onCancel,
  } = useEdComRightForm<IMessageParticipantsInput>();

  const isParticipantsReadonly = useIsParticipantsReadonly();

  const _onSubmit = useCallback<
    (values: MessagesParticipantsTabEntity) => void
  >(
    ({ participantsAdded, participantsRemoved }) => {
      if (isParticipantsReadonly) {
        onGoNext();
      } else if (selectedItem?.type && selectedItem.id && onSubmit) {
        const _participantsAdded = participantsAdded
          ?.filter(({ id }) => typeof id === 'string')
          .map(({ id }) => id);
        return onSubmit({
          type: selectedItem.type,
          messageId: selectedItem.id,
          participantsAdded: _participantsAdded,
          participantsRemoved: map(participantsRemoved, 'id'),
        });
      } else {
        setTempData({
          participants: [
            ...(tempData.participants || []),
            ...participantsAdded,
          ],
        });
        onGoNext();
      }
    },
    [
      setTempData,
      isParticipantsReadonly,
      onGoNext,
      onSubmit,
      selectedItem,
      tempData,
    ],
  );

  const [_participants, _setParticipants] = useState<IRecipientMinimal[]>(
    tempData?.participants ? [...tempData?.participants, me] : [me],
  );

  const handleParticipants = useCallback(
    (participants: IRecipientMinimal[]) => {
      if (selectedItem?.id && tempData?.participants) {
        const mergedParticipants = unionBy(
          tempData.participants,
          participants,
          'id',
        );
        _setParticipants(mergedParticipants);
      } else {
        _setParticipants(participants);
      }
    },
    [selectedItem, tempData],
  );

  const _entity = useMemo<MessagesParticipantsTabEntity>(
    () => ({
      participants: _participants,
      participantsRemoved: [],
      participantsAdded: [],
      id: selectedItem?.id,
    }),
    [_participants, selectedItem],
  );

  const _isSubmitDisabled = useCallback(
    (isDisabled: boolean, formState) => {
      const { isSubmitting, dirty } = formState;
      if (isChannel(selectedItem)) {
        return isSubmitting;
      }
      return isDisabled || !dirty;
    },
    [selectedItem],
  );

  return (
    <EntityForm<MessagesParticipantsTabEntity>
      hasLeavePrompt
      isNew
      createLabel={t('Next')}
      entity={_entity}
      hasCreateMessage={!isParticipantsReadonly && !!selectedItem?.id}
      isSubmitDisabled={_isSubmitDisabled}
      successMessageText={t('Updated successfully')}
      onCancel={onCancel}
      onSubmit={_onSubmit}
    >
      <MessagesParticipantsTabBody setParticipants={handleParticipants} />
    </EntityForm>
  );
};

export default MessagesParticipantsTab;

interface MessagesParticipantsTabEntity extends IBasicEntity {
  participants: IRecipientMinimal[];
  participantsAdded: IRecipientMinimal[];
  participantsRemoved: IRecipientMinimal[];
}
