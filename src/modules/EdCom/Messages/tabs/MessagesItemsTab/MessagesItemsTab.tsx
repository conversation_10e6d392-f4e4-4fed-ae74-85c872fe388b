import React, { FC } from 'react';
import IMessage from '../../../../../common/abstract/Messages/IMessage';
import MessageItems from '../../../../../common/components/forms/MessageItems';
import useIsMessageReadonly from '../../../../../common/components/forms/MessageItems/hooks/useIsMessageReadonly';
import useEdComSelectedItem from '../../../EdComCrud/context/EdComSelectedItem';

const MessagesItemsTab: FC = () => {
  const { selectedItem } = useEdComSelectedItem<IMessage>();
  const isMessageReadonly = useIsMessageReadonly(selectedItem);

  return (
    <MessageItems
      isMessageReadonly={isMessageReadonly}
      selectedItem={selectedItem}
    />
  );
};

export default MessagesItemsTab;
