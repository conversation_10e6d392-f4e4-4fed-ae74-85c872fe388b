.attachedArea > li {
  display: inline-block;
}

.container {
  border: 1px solid #e1e1e1;
  border-top: none;

  &:first-child {
    border-top: 1px solid #e1e1e1;
  }
}

.main {
  display: flex;
  justify-content: space-between;
  min-height: 70px;
  padding: 20px;
}

.threadBorder {
  border-top: 1px solid #e1e1e1;

  &:first-child {
    border-top: none !important;
  }
}

.reply {
  padding-left: 50px;
}

.hasBottomSeparator {
  border-bottom: none;
}

.hasBottomActions {
  padding-bottom: 0;
}

.contentContainer {
  display: flex;
}

.content {
  display: flex;
  flex-direction: column;
  word-break: break-all;
}

.rightPart {
  min-width: 80px;
  justify-content: space-between;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.bottomActionContainer {
  display: flex;
  height: 20px;
}

@media (max-width: 500px) {
  .attachedArea audio {
    width: 176px;
  }
}

@media (max-width: 768px) {
  .bottomActionContainer {
    height: 36px;
  }
}

.bottomAction {
  cursor: pointer;
  color: #1e88e5;
  margin-right: 5px;
}

.newMessage {
  color: #1e88e5;
}

.tooltip {
  display: flex;
}

.modalWrapper {
  :global(.swal2-actions) {
    justify-content: right !important;
  }
}