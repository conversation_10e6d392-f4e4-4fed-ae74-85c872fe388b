import React, { FC, useCallback, useEffect, useMemo, useState } from 'react';
import { get, map } from 'lodash';
import { useHistory } from 'react-router-dom';
import { useQuery } from 'react-apollo';
import useT from '../../../../../common/components/utils/Translations/useT';
import useEdComSelectedItem from '../../../EdComCrud/context/EdComSelectedItem';
import useEdComTempItem from '../../../EdComCrud/context/EdComTempItem';
import useEdComRightForm from '../../../EdComCrud/context/EdComRightForm';

import EntityForm from '../../../../../common/components/containers/EntityForm';
import { IBasicEntity } from '../../../../../common/components/containers/EntityForm/EntityForm';
import IConversation from '../../abstract/IConversation';
import IConversationTemp from '../../abstract/IConversationTemp';
import IConversationInput from '../../abstract/IConversationInput';
import TConversationStatus from '../../abstract/TConversationStatus';
import TConversationTypePartCount from '../../abstract/TConversationTypePartCount';
import { IFileAttachmentTemp } from '../../../../../common/abstract/IFileAttachments';
import SpinnerError from '../../../../../common/components/utils/SpinnerError';
import useCurrentUser from '../../../../../common/data/hooks/useCurrentUser';
import ConversationsParticipantsTabBody from './ConversationsParticipantsTabBody';
import IRecipientMinimal from '../../../../../common/abstract/EdCom/IRecipientMinimal';
import conversationOneToOneGql from '../../data/conversationOneToOne.graphql';
import { ONE_TO_ONE } from '../../model/ConversationTypePartCount';
import useEdComTabCoreUrls from '../../../EdComCrud/context/EdComTabCoreUrls';
import getGqlOperationName from '../../../../../common/utils/getGqlOperationName';

const ConversationsParticipantsTab: FC = () => {
  const t = useT();
  const { loading, me, error } = useCurrentUser();
  const { selectedItem } = useEdComSelectedItem<IConversation>();
  const { tempData, setTempData } = useEdComTempItem<IConversationTemp>();
  const {
    onGoNext,
    onCancel,
    onSubmit,
  } = useEdComRightForm<IConversationInput>();
  const { listRootUrl } = useEdComTabCoreUrls();
  const { push } = useHistory();

  const { data, loading: otoLoading, error: otoError } = useQuery(
    conversationOneToOneGql,
    {
      variables: { personId: selectedItem?.oneToOnePerson?.id },
      skip:
        selectedItem?.typePartCount !== ONE_TO_ONE.value ||
        !selectedItem?.oneToOnePerson?.id,
    },
  );

  useEffect(() => {
    const oneToOneKey = getGqlOperationName(conversationOneToOneGql);
    const oneToOne = get(data, oneToOneKey);
    if (oneToOne) {
      push(`${listRootUrl}/${oneToOne.id}`);
    }
  }, [data, listRootUrl, push]);

  const defaultParticipants = useMemo(() => (me && [me]) || [], [me]);

  const _onSubmit = useCallback<
    (values: ConversationsParticipantsTabEntity) => Promise<void> | void
  >(
    ({ participantsAdded, participantsRemoved, ...entity }) => {
      if (selectedItem?.id && onSubmit) {
        const _participantsAdded = map(participantsAdded, 'id');
        const _participantsRemoved = map(participantsRemoved, 'id');

        return onSubmit({
          id: selectedItem?.id,
          topic: entity.topic,
          status: entity.status,
          typePartCount: entity.typePartCount,
          organisationId: entity.organisationId,
          personEntityAllocationId: entity.personEntityAllocationId,
          dateFrom: entity.dateFrom,
          dateTo: entity.dateTo,
          logoAttachment: entity.logoAttachment
            ? {
                fileId: entity.logoAttachment.fileId,
                description: entity.logoAttachment.description,
                fileName: entity.logoAttachment.fileName,
              }
            : undefined,
          participantsAdded: _participantsAdded,
          participantsRemoved: _participantsRemoved,
        });
      }

      setTempData({
        participantsAdded: selectedItem?.id
          ? participantsAdded
          : [...participantsAdded, ...defaultParticipants],
        participantsRemoved,
      });
      onGoNext();
    },
    [setTempData, defaultParticipants, onGoNext, selectedItem, onSubmit],
  );

  const [_participants, _setParticipants] = useState<IRecipientMinimal[]>(
    tempData.participants ||
      selectedItem?.participantsAdded ||
      defaultParticipants,
  );

  // Update participants when selectedItem changes (e.g., when a contact is selected)
  useEffect(() => {
    if (selectedItem?.participantsAdded && !tempData.participants) {
      _setParticipants(selectedItem.participantsAdded);
    }
  }, [selectedItem, tempData.participants]);

  const _entity = useMemo<ConversationsParticipantsTabEntity>(
    () => ({
      participants: _participants,
      participantsRemoved: [],
      participantsAdded: [],
      id: selectedItem?.id,
      topic: selectedItem?.topic || '',
      status: selectedItem?.status || 'ACTIVE',
      typePartCount: selectedItem?.typePartCount || 'GROUP',
      organisationId: selectedItem?.organisationId,
      personEntityAllocationId: selectedItem?.personEntityAllocationId,
      dateFrom: selectedItem?.dateFrom,
      dateTo: selectedItem?.dateTo,
      logoAttachment: selectedItem?.logoAttachment as IFileAttachmentTemp,
    }),
    [_participants, selectedItem],
  );

  const isSubmitDisabled = useCallback(
    (isDisabled: boolean, formState) => {
      if (_entity.participants.length > 1 && !_entity.id) {
        return false;
      }
      const { dirty, isSubmitting } = formState;
      return isSubmitting || (!dirty && !isSubmitting);
    },
    [_entity],
  );

  return (
    <SpinnerError error={error || otoError} loading={loading || otoLoading}>
      <EntityForm<ConversationsParticipantsTabEntity>
        hasLeavePrompt
        isNew
        createLabel={t('Next')}
        entity={_entity}
        hasCreateMessage={!!selectedItem?.id}
        isSubmitDisabled={isSubmitDisabled}
        successMessageText={t('Updated successfully')}
        onCancel={onCancel}
        onSubmit={_onSubmit}
      >
        <ConversationsParticipantsTabBody setParticipants={_setParticipants} />
      </EntityForm>
    </SpinnerError>
  );
};

export default ConversationsParticipantsTab;

interface ConversationsParticipantsTabEntity extends IBasicEntity {
  participants: IRecipientMinimal[];
  participantsAdded: IRecipientMinimal[];
  participantsRemoved: IRecipientMinimal[];
  topic: string;
  status: TConversationStatus;
  typePartCount: TConversationTypePartCount;
  organisationId?: number;
  personEntityAllocationId?: number;
  dateFrom?: Date;
  dateTo?: Date;
  logoAttachment?: IFileAttachmentTemp;
}
