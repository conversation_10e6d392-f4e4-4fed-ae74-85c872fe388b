import React, { useCallback, useMemo } from 'react';
import { useMutation } from 'react-apollo';
import { ApolloError } from 'apollo-client';
import classnames from 'classnames';
import { handleFormErrors } from '../../../../../src/common/errors';
import RoundedLinkButton from '../../../../common/components/controls/RoundedLinkButton';
import useT from '../../../../common/components/utils/Translations/useT';
import swal2 from '../../../../common/utils/swal2';
import { ACTIVE } from '../model/ConversationStatus';
import useEdComGql from '../../EdComCrud/context/EdComGql';
import useEdComRightForm from '../../EdComCrud/context/EdComRightForm';
import useEdComSelectedItem from '../../EdComCrud/context/EdComSelectedItem';

import IConversation from '../abstract/IConversation';
import useIsCurrUserOwner from './useIsCurrUserOwner';
import leaveConversationGql from '../data/leaveConversation.graphql';
import deleteConversationGql from '../data/deleteConversation.graphql';
import useCurrentUser from '../../../../common/data/hooks/useCurrentUser';
import styles from './useDeleteOrLeftConversation.scss';

export default function useDeleteOrLeftConversation(): IUseDeleteOrLeftConversationRes {
  const t = useT();
  const { loading: uLoading, error: uError } = useCurrentUser();

  const { selectedItem } = useEdComSelectedItem<IConversation>();
  const { onCancel } = useEdComRightForm<IConversation>();
  const { refetchQueries } = useEdComGql();

  const isDeleting = useIsCurrUserOwner();

  const [mutation, { loading, error }] = useMutation<{
    deleteMessage: IDeleteResponse;
  }>(isDeleting ? deleteConversationGql : leaveConversationGql, {
    refetchQueries,
  });

  const inputPlaceholder = isDeleting
    ? t('Delete for other participants as well')
    : undefined;

  const buttonLabel = useMemo<string>(
    () => t(isDeleting ? 'Delete' : 'Leave'),
    [isDeleting, t],
  );

  const swalTitle = useMemo<string>(
    () =>
      t(
        isDeleting
          ? 'Are you sure you want to delete this Conversation?'
          : 'Are you sure you want to leave this Conversation?',
      ),
    [t, isDeleting],
  );

  const confirm = useCallback<
    () => Promise<{ isConfirmed: boolean; value?: boolean }>
  >(async () => {
    const { isConfirmed, value } = await swal2<1 | 0>({
      icon: 'warning',
      deleteMode: true,
      input: isDeleting ? 'checkbox' : undefined,
      inputPlaceholder,
      title: swalTitle,
      confirmButtonText: buttonLabel,
      cancelButtonText: t('Cancel'),
      className: classnames(styles.modalWrapper),
    });

    return {
      isConfirmed,
      value: isDeleting && !!value,
    };
  }, [swalTitle, t, inputPlaceholder, buttonLabel, isDeleting]);

  const runAction = useCallback<(forEveryone?: boolean) => Promise<void>>(
    isHardDelete =>
      selectedItem &&
      handleFormErrors(
        mutation({
          variables: isDeleting
            ? { id: selectedItem.id, isHardDelete }
            : { id: selectedItem.id },
        }),
        t,
      ),
    [t, selectedItem, mutation, isDeleting],
  );

  const handleAction = useCallback(async () => {
    if (selectedItem?.id) {
      const { value, isConfirmed } = await confirm();

      if (isConfirmed) {
        await runAction(value);
        onCancel();
      }
    }
  }, [onCancel, runAction, confirm, selectedItem]);

  const button = useMemo<JSX.Element | undefined>(() => {
    if (selectedItem?.id && selectedItem.status === ACTIVE.value) {
      return (
        <RoundedLinkButton
          additionClasses="text-danger"
          title={buttonLabel}
          onClick={handleAction}
        >
          {buttonLabel}
        </RoundedLinkButton>
      );
    }

    return undefined;
  }, [selectedItem, handleAction, buttonLabel]);

  return {
    button,
    loading: loading || uLoading,
    error: error || uError,
  };
}

interface IUseDeleteOrLeftConversationRes {
  button?: JSX.Element;
  loading: boolean;
  error?: ApolloError;
}
