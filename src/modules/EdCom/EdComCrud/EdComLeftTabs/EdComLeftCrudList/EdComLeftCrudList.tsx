import { ApolloError } from 'apollo-client';
import React, { useCallback, useMemo } from 'react';
import classnames from 'classnames';
import { useMutation } from 'react-apollo';
import PaginationFooter from '../../../../../common/components/other/PaginationFooter';
import SpinnerError from '../../../../../common/components/utils/SpinnerError';
import useT from '../../../../../common/components/utils/Translations/useT';
import dummyMutation from '../../../../../common/data/dummyMutation.graphql';
import useQueryWithCount from '../../../../../common/data/useQueryWithCount';
import { handleFormErrors } from '../../../../../common/errors';
import swal2 from '../../../../../common/utils/swal2';
import IWithId from '../../abstract/IWithId';
import IWith<PERSON>earchField from '../../../../../common/abstract/EdCom/IWithSearchField';
import useEdComFilterBar from '../../context/EdComFilterBar';
import useEdComGql from '../../context/EdComGql';
import useEdComList from '../../context/EdComList';
import EdComLeftCrudFilters from './EdComLeftCrudFilters';
import styles from './EdComLeftCrudList.scss';
import EdComLeftCrudListItems from './EdComLeftCrudListItems';

export default function EdComLeftCrudList<
  TEntity extends IWithId,
  TFilter extends IWithSearchField,
  TVariables extends IWithSearchField = TFilter
>({
  createError,
  createLoading,
  getName,
}: Readonly<IEdComLeftCrudListProps<TEntity>>) {
  const { pageNumber, setPageNumber, pageSize } = useEdComFilterBar<TFilter>();

  const {
    deleteMutation,
    _itemsQuery,
    refetchQueries,
    cookDeleteVariables,
  } = useEdComGql<TFilter, TVariables, TEntity>();

  const { deleteCheckboxText, deleteTitleText } = useEdComList();

  const {
    items,
    itemsCount,
    loading: itemsLoading,
    error: itemsError,
  } = useQueryWithCount<TEntity>(
    _itemsQuery.query,
    _itemsQuery.queryCount,
    {
      variables: _itemsQuery.variables,
      skip: _itemsQuery.skip,
    },
    _itemsQuery.pagination,
  );

  const [deleteCall, { loading: deleteLoading }] = useMutation<{
    id: number | string;
    deleted: boolean;
  }>(deleteMutation || dummyMutation, {
    awaitRefetchQueries: true,
    refetchQueries,
  });

  const t = useT();

  const handleDelete = useCallback<(item: TEntity) => Promise<boolean>>(
    async (item: TEntity) => {
      const inputPlaceholder: string | undefined = deleteCheckboxText
        ? deleteCheckboxText(item)
        : undefined;

      const title: string = deleteTitleText
        ? deleteTitleText(item)
        : t('Are you sure you want to delete #{name}?', {
            name: getName ? getName(item) : item.name,
          });

      const { isConfirmed, value } = await swal2<1 | 0>({
        icon: 'warning',
        title,
        confirmButtonText: t('Delete'),
        cancelButtonText: t('Cancel'),
        inputPlaceholder,
        input: inputPlaceholder ? 'checkbox' : undefined,
        deleteMode: true,
        className: classnames(styles.modalWrapper),
      });

      if (isConfirmed) {
        await handleFormErrors(
          deleteCall({
            variables: cookDeleteVariables
              ? cookDeleteVariables(item, !!inputPlaceholder && !!value)
              : { id: item.id },
          }),
          t,
        );

        return true;
      }

      return false;
    },
    [
      deleteTitleText,
      cookDeleteVariables,
      deleteCheckboxText,
      getName,
      deleteCall,
      t,
    ],
  );

  const error = useMemo(() => itemsError || createError, [
    itemsError,
    createError,
  ]);

  const loading = useMemo<boolean>(
    () => itemsLoading || deleteLoading || !!createLoading,
    [deleteLoading, itemsLoading, createLoading],
  );

  const firstLoading = useMemo<boolean>(
    () => loading && items?.length === 0 && itemsCount === 0,
    [loading, items, itemsCount],
  );

  return (
    <div>
      <EdComLeftCrudFilters<TFilter> key="EdComLeftCrudFilters" />

      <SpinnerError
        error={error}
        firstLoading={firstLoading}
        inline={false}
        loading={loading}
      >
        <EdComLeftCrudListItems<TEntity>
          handleDelete={deleteMutation ? handleDelete : undefined}
          items={items}
        />
        {itemsCount > 0 ? (
          <PaginationFooter
            className={styles.paginator}
            itemsCount={itemsCount}
            loading={itemsCount === 0 && itemsLoading}
            maxDisplayPagesCount={3}
            pageNumber={pageNumber}
            pageSize={pageSize}
            setPageNumber={setPageNumber}
          />
        ) : null}
      </SpinnerError>
    </div>
  );
}

interface IEdComLeftCrudListProps<TEntity> {
  createLoading?: boolean;
  createError?: ApolloError;
  getName?: (item: TEntity) => string;
}
