.container {
  display: flex;
  // margin-top: 42px;
}

.left {
  max-width: 330px;
  margin-right: 15px;

  :global(div.dropdown-menu) {
    position: absolute !important;
    overflow: visible !important;
    top: 100% !important;
    left: 0 !important;
    height: 100% !important;
    min-height: max-content !important;
    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.16), 0 3px 6px rgba(0, 0, 0, 0.23),
      0 -1px 1px rgba(0, 0, 0, 0.1);
  }

  :global(div.dropdown-menu > div.dropdown-menu) {
    position: relative !important;
    overflow: hidden !important;
    top: 0% !important;
    height: 100% !important;
    box-shadow: none;
  }

  :global(div.dropdown-menu > ul) {
    position: relative !important;
    top: 0 !important;
    margin: 0;
    box-shadow: none;
  }
}

.right {
  flex-grow: 1;
  width: 10px; // for correct grow
}

@media all and (max-width: 1000px) {
  .container {
    flex-direction: column;
    clear: left;
  }
  .left {
    max-width: 100%;
    margin-right: 0;
  }
  .right {
    width: 100%;
  }
}

@media all and (max-width: 768px) {
  .container {
    :global(.nav-tabs) {
      border-radius: 3px 3px 0 0;
      box-shadow: none;
      border-bottom: 1px solid #ddd;
      padding-bottom: 0;
    }
  }
  .right {
    :global(.panel-body) {
      border: 0;
    }
  }
}

@media (min-width: 1000px) {
  :global(.content-wrapper) {
    display: grid !important;
  }
}
