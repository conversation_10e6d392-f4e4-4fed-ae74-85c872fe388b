import React, { FC, useCallback, useMemo, useState } from 'react';
import jsPDF from 'jspdf';
import autoTable from 'jspdf-autotable';
import { isEmpty, isEqual, isNumber } from 'lodash';
import { useQuery } from 'react-apollo';
import classNames from 'classnames';

import { useTranslation } from '../../../../../../../../common/components/utils/Translations';
import getCurriculumStructureContentsGql from '../../data/getCurriculumStructureContents.graphql';
import getCurriculumStructureContentsCountGql from '../../data/getCurriculumStructureContentsCount.graphql';
import programStructureLPTreeGql from '../../../../../../../../common/components/controls/FilterBar/ProgramStructureLPTreeSelector/data/programStructureLPTree.graphql';
import GqlFullCrudTable from '../../../../../../../../common/components/dataViews/GqlFullCrudTable';
import usePaginationRouting from '../../../../../../../../common/data/hooks/usePaginationRouting';
import ILearningPlanTask from '../../../../../../../../common/abstract/OrganisationGroup/ProgramGroups/ILearningPlanTask';
import columns, { ICurriculumStructureContentsTable } from './columns';
import { redirectNewTab } from '../../../../../../../../common/utils/urlHelper';
import Spinner from '../../../../../../../../common/components/utils/Spinner';
import { ICurriculumStructureTable } from '../../CurriculumStructureModuleView/columns';
import { PDF_TABLE_STYLES } from '../../CurriculumStructure';
import RoundedDropdownButton from '../../../../../../../../common/components/controls/RoundedDropdownButton';
import {
  isLearningPlanTask,
  resolveNodeId,
  resolveNodeParentId,
} from '../../../../../../../../common/components/controls/FilterBar/ProgramStructureLPTreeSelector/ProgramStructureLPTreeAdapter';
import nodeSortingOptionsSequence from '../../../../../../../../common/utils/nodeSortingOptionsSequence';
import orderBy from '../../../../../../../../common/utils/orderBy';
import styles from './styles.scss';

const LPContentTable: FC<{
  node: ILearningPlanTask;
  programStructureLPIds: number[];
  programGroupName: string;
  programGroupId: number;
}> = ({ node, programStructureLPIds, programGroupName, programGroupId }) => {
  const { t } = useTranslation();
  const { pageNumber, pageSize } = usePaginationRouting();

  const _columns = useMemo(() => columns(), []);

  const wrapperComponent = useCallback(
    ({ children }) => <div>{children}</div>,
    [],
  );

  const openItem = useCallback((row: ICurriculumStructureContentsTable) => {
    const link = `/e-content/libraries/edit/${row.libraryId}/edit/${row.lessonGroupId}/contents-list/edit/${row.lessonId}/content`;
    redirectNewTab(link);
  }, []);

  const handleRowClick = useCallback(
    model => {
      openItem(model);
    },
    [openItem],
  );

  const [itemCount, setItemCount] = useState();

  const [lpIds, setLpIds] = useState<number[]>([]);
  const [fetchAllRecords, setFetchAllRecords] = useState(false);
  const [downloadingPdf, setDownloadingPdf] = useState(false);

  // Query to fetch tree structure with sequence information
  const { data: treeData } = useQuery(programStructureLPTreeGql, {
    variables: {
      programGroupId,
      view: 'CONTENT',
      withTaskIds: true,
      programStructureLPTaskIds: programStructureLPIds,
    },
    skip: !isNumber(programGroupId),
    fetchPolicy: 'cache-first',
  });

  // Function to get sorted module IDs by tree sequence using proper tree traversal
  const getSortedModuleIds = useCallback(
    (moduleIds: number[]) => {
      if (!treeData?.programStructureLPTree) {
        return moduleIds;
      }

      // Build a proper tree structure and traverse it to get modules in hierarchical order
      const nodeMap = new Map();
      const childrenMap = new Map();

      // Create node map and children map
      treeData.programStructureLPTree.forEach((node: any) => {
        nodeMap.set(resolveNodeId(node), node);
        const parentId = resolveNodeParentId(node);
        if (parentId) {
          if (!childrenMap.has(parentId)) {
            childrenMap.set(parentId, []);
          }
          childrenMap.get(parentId).push(resolveNodeId(node));
        }
      });

      // Sort children at each level using nodeSortingOptionsSequence
      const sortChildren = (nodeIds: string[]) => {
        const nodes = nodeIds.map(id => nodeMap.get(id)).filter(Boolean);
        const sortedNodes = orderBy(
          nodes,
          nodeSortingOptionsSequence.iteratees,
          nodeSortingOptionsSequence.orders,
        );
        return sortedNodes.map(node => resolveNodeId(node));
      };

      // Sort all children arrays
      for (const [parentId, children] of childrenMap.entries()) {
        childrenMap.set(parentId, sortChildren(children));
      }

      // Recursive function to traverse tree and collect LearningPlanTask IDs in hierarchical order
      const collectModuleIds = (nodeId: string): number[] => {
        const node = nodeMap.get(nodeId);
        const moduleIds: number[] = [];

        if (node && isLearningPlanTask(node)) {
          moduleIds.push(node.id);
        }

        const children = childrenMap.get(nodeId) || [];
        for (const childId of children) {
          moduleIds.push(...collectModuleIds(childId));
        }

        return moduleIds;
      };

      // Start from root nodes and collect all module IDs in hierarchical order
      const rootNodeIds = treeData.programStructureLPTree
        .filter((node: any) => !resolveNodeParentId(node))
        .map((node: any) => resolveNodeId(node));

      const sortedRootIds = sortChildren(rootNodeIds);
      const allSortedModuleIds: number[] = [];

      for (const rootId of sortedRootIds) {
        allSortedModuleIds.push(...collectModuleIds(rootId));
      }

      // Filter to only include the module IDs we're interested in, maintaining order
      return allSortedModuleIds.filter(id => moduleIds.includes(id));
    },
    [treeData, programStructureLPIds],
  );

  const createAndSavePdf = useCallback(
    (head, body) => {
      const doc = new jsPDF();
      doc.setFontSize(PDF_TABLE_STYLES.DOC_FONT_SIZE);
      doc.text(
        t('Curriculum Structure'),
        PDF_TABLE_STYLES.HEADING_X,
        PDF_TABLE_STYLES.HEADING_Y,
      );
      autoTable(doc, {
        startY: PDF_TABLE_STYLES.TABLE_TOP_SPACE,
        head: [head],
        body,
        headStyles: {
          fillColor: PDF_TABLE_STYLES.HEAD_FILL_COLOR as [
            number,
            number,
            number,
          ],
          textColor: [0, 0, 0],
          fontStyle: 'normal',
        },
        styles: {
          cellPadding: { top: 3, bottom: 3, left: 2, right: 2 },
          valign: 'middle',
        },
        columnStyles: {
          0: { cellWidth: 55, halign: 'left' },
          1: { cellWidth: 65, halign: 'left' },
          2: { cellWidth: 65, halign: 'left' },
        },
      });
      doc.save('Curriculum Structure.pdf');
      setDownloadingPdf(false);
    },
    [t],
  );

  const createAndSavePdfByModule = useCallback(
    (head, bodies) => {
      const doc = new jsPDF();
      doc.setFontSize(PDF_TABLE_STYLES.DOC_FONT_SIZE);

      // Initial heading
      doc.text(
        t('Curriculum Structure'),
        PDF_TABLE_STYLES.HEADING_X,
        PDF_TABLE_STYLES.HEADING_Y,
      );

      let currentY =
        PDF_TABLE_STYLES.HEADING_Y + PDF_TABLE_STYLES.TABLES_SPACE_BETWEEN; // Add some space after heading

      doc.setFontSize(PDF_TABLE_STYLES.PATH_FONT_SIZE);
      for (const i of bodies) {
        const splitText = doc.splitTextToSize(
          i.path,
          PDF_TABLE_STYLES.TEXT_MAX_WIDTH,
        );

        doc.text(splitText, PDF_TABLE_STYLES.HEADING_X, currentY);

        const textHeight =
          (splitText.length - 1) * PDF_TABLE_STYLES.TEXT_LINE_HEIGHT;
        currentY += textHeight + PDF_TABLE_STYLES.LABEL_TABLE_TOP_SPACE;

        autoTable(doc, {
          startY: currentY,
          head: [head],
          body: i.body,
          headStyles: {
            fillColor: PDF_TABLE_STYLES.HEAD_FILL_COLOR as [
              number,
              number,
              number,
            ],
            textColor: [0, 0, 0],
            fontStyle: 'normal',
          },
          styles: {
            cellPadding: { top: 3, bottom: 3, left: 2, right: 2 },
            valign: 'middle',
          },
          columnStyles: {
            0: { cellWidth: 55, halign: 'left' },
            1: { cellWidth: 65, halign: 'left' },
            2: { cellWidth: 65, halign: 'left' },
          },
        });

        // Update currentY to the end of this table
        currentY =
          (doc as any).lastAutoTable.finalY +
          PDF_TABLE_STYLES.TABLES_SPACE_BETWEEN; // add spacing between tables
      }

      doc.save('Curriculum Structure.pdf');
      setDownloadingPdf(false);
    },
    [t],
  );

  const computedTableData = useCallback(
    data => {
      const head = _columns.map(col => t(col.title as string));
      const body = data.map((item: ICurriculumStructureTable) =>
        _columns.map(col => item[col.id]),
      );
      return { head, body };
    },
    [_columns, t],
  );

  const computedTableDataByModule = useCallback(
    data => {
      const head = _columns.map(col => t(col.title as string));

      const groupedByModule = data.reduce((acc, item) => {
        const key = item.moduleId;
        if (!acc[key]) {
          acc[key] = [];
        }
        acc[key].push(item);
        return acc;
      }, {});

      // Get module IDs and sort them by tree sequence
      const moduleIds = Object.keys(groupedByModule).map(id =>
        parseInt(id, 10),
      );
      const sortedModuleIds = getSortedModuleIds(moduleIds);

      // Create bodies array in the sorted order
      const bodies = sortedModuleIds.map(moduleId => {
        const key = moduleId.toString();
        const items = groupedByModule[key] as ICurriculumStructureTable[];

        const body = items.map((item: ICurriculumStructureTable) =>
          _columns.map(col => item[col.id]),
        );
        const path = `${programGroupName} > ${items[0].programName} > ${items[0].subjectName} > ${items[0].learningPlanName} > ${items[0].learningPlanCategoryName} > ${items[0].moduleName}`;

        return {
          key,
          path,
          body,
        };
      });

      return { head, bodies };
    },
    [_columns, t, programGroupName, getSortedModuleIds],
  );

  const onCompleted = useCallback(
    data => {
      if (data) {
        if (isEqual(lpIds, [node.id])) {
          const { head, body } = computedTableData(
            data.getCurriculumStructureContents
              ? data.getCurriculumStructureContents
              : data,
          );
          createAndSavePdf(head, body);
        } else {
          const { head, bodies } = computedTableDataByModule(
            data.getCurriculumStructureContents
              ? data.getCurriculumStructureContents
              : data,
          );
          createAndSavePdfByModule(head, bodies);
        }
        setLpIds([]);
      }
    },
    [
      createAndSavePdfByModule,
      computedTableDataByModule,
      computedTableData,
      createAndSavePdf,
      lpIds,
      node.id,
    ],
  );

  const { loading } = useQuery(getCurriculumStructureContentsGql, {
    variables: {
      ids: lpIds,
      first: 0,
      count: 100000,
      searchQuery: '',
    },
    skip: !isNumber(node.id) || !fetchAllRecords || isEmpty(lpIds),
    onCompleted,
  });

  const downloadCurrentViewAsPdf = useCallback(() => {
    setDownloadingPdf(true);
    setLpIds([node.id]);
    setFetchAllRecords(true);
    setDownloadingPdf(false);
  }, [node.id]);

  const downloadAllAsPdf = useCallback(() => {
    setDownloadingPdf(true);
    setLpIds(programStructureLPIds);
    setFetchAllRecords(true);
    setDownloadingPdf(false);
  }, [programStructureLPIds]);

  const cookCount = useCallback(count => {
    setItemCount(count);
    return count;
  }, []);

  const renderFooter = useCallback(() => {
    const actions = [
      {
        text: t('Current View'),
        onClick: downloadCurrentViewAsPdf,
      },
      {
        text: t('Full Report'),
        onClick: downloadAllAsPdf,
      },
    ];
    return (
      <div className={classNames('pull-right', styles.mb100)}>
        <RoundedDropdownButton
          clickableBtn
          isSubmitTogglesDropdown
          actions={actions}
          disabled={downloadingPdf || loading || !itemCount}
          // eslint-disable-next-line @typescript-eslint/ban-ts-ignore
          // @ts-ignore
          type="button"
        >
          {downloadingPdf || loading ? <Spinner /> : t('Download as PDF')}
        </RoundedDropdownButton>
      </div>
    );
  }, [
    t,
    downloadAllAsPdf,
    downloadCurrentViewAsPdf,
    downloadingPdf,
    loading,
    itemCount,
  ]);

  return (
    <GqlFullCrudTable
      hasPagination
      gql={{
        query: getCurriculumStructureContentsGql,
        count: getCurriculumStructureContentsCountGql,
      }}
      list={{
        cookCount,
        onRowClick: handleRowClick,
        hasSearchFieldOnly: true,
        initialFilter: {
          ids: [node.id],
          first: (Number(pageNumber) - 1) * pageSize,
          count: pageSize,
        },
        wrapper: wrapperComponent,
        tableConfig: {
          columns: _columns,
        },
        renderFooter,
      }}
      title={t('Curriculum Structure')}
    />
  );
};

export default LPContentTable;
